# frozen_string_literal: true

require 'active_support/core_ext/integer/time'

Rails.application.configure do
  # Settings specified here will take precedence over those in config/application.rb.

  # Code is not reloaded between requests.
  config.enable_reloading = false

  # Eager load code on boot for better performance and memory savings (ignored by Rake tasks).
  config.eager_load = true

  # Full error reports are disabled.
  config.consider_all_requests_local = false

  # Turn on fragment caching in view templates.
  config.action_controller.perform_caching = true

  # Ensures that a master key has been made available in ENV["RAILS_MASTER_KEY"], config/master.key, or an environment
  # key such as config/credentials/production.key. This key is used to decrypt credentials (and other encrypted files).
  # config.require_master_key = true

  # Disable serving static files from `public/`, relying on NGINX/Apache to do so instead.
  # config.public_file_server.enabled = false

  # Compress CSS using a preprocessor.
  # config.assets.css_compressor = :sass

  # Do not fall back to assets pipeline if a precompiled asset is missed.
  config.assets.compile = false

  # Enable serving of images, stylesheets, and JavaScripts from an asset server.
  config.asset_host = ENV.fetch('ASSETS_CLOUDFRONT_URL')
  config.assets.prefix = ENV.fetch('ASSETS_PREFIX')

  # Specifies the header that your server uses for sending files.
  # config.action_dispatch.x_sendfile_header = "X-Sendfile" # for Apache
  # config.action_dispatch.x_sendfile_header = "X-Accel-Redirect" # for NGINX

  # Store uploaded files on the local file system (see config/storage.yml for options).
  config.active_storage.service = :amazon

  # Mount Action Cable outside main process or domain.
  # config.action_cable.mount_path = nil
  # config.action_cable.url = "wss://example.com/cable"
  # config.action_cable.allowed_request_origins = [ "http://example.com", /http:\/\/example.*/ ]

  # Assume all access to the app is happening through a SSL-terminating reverse proxy.
  # Can be used together with config.force_ssl for Strict-Transport-Security and secure cookies.
  # config.assume_ssl = true

  # Force all access to the app over SSL, use Strict-Transport-Security, and use secure cookies.
  config.force_ssl = true
  config.ssl_options = { redirect: { exclude: ->(request) { request.path =~ %r{^/up$} } } }

  # Log to STDOUT by default
  config.logger = ActiveSupport::Logger.new($stdout)
  #   .tap  { |logger| logger.formatter = Logger::Formatter.new }
  #   .then { |logger| ActiveSupport::TaggedLogging.new(logger) }

  # Prepend all log lines with the following tags.
  # config.log_tags = [:request_id]

  # "info" includes generic and useful information about system operation, but avoids logging too much
  # information to avoid inadvertent exposure of personally identifiable information (PII). If you
  # want to log everything, set the level to "debug".
  config.log_level = ENV.fetch('RAILS_LOG_LEVEL', 'info')

  # Prevent health checks from clogging up the logs
  config.silence_healthcheck_path = "/up"

  # Use Redis for cache store
  config.cache_store = :redis_cache_store,
                       { driver: :hiredis, url: RedisConfig.url, namespace: 'cache', expires_in: 1.day,
                         pool: { size: Integer(ENV.fetch("RAILS_MAX_THREADS", 5)), timeout: 5 }, }

  # Use a real queuing backend for Active Job (and separate queues per environment).
  # config.active_job.queue_adapter = :resque
  # config.active_job.queue_name_prefix = "core_production"

  config.action_mailer.perform_caching = false

  # Ignore bad email addresses and do not raise email delivery errors.
  # Set this to true and configure the email server for immediate delivery to raise delivery errors.
  # config.action_mailer.raise_delivery_errors = false

  # Enable locale fallbacks for I18n (makes lookups for any locale fall back to
  # the I18n.default_locale when a translation cannot be found).
  config.i18n.fallbacks = true

  # Don't log any deprecations.
  config.active_support.report_deprecations = false

  # Do not dump schema after migrations.
  config.active_record.dump_schema_after_migration = false

  # Robots should not visit our staging site.
  config.action_dispatch.default_headers['X-Robots-Tag'] = 'noindex, nofollow, nosnippet, noarchive'

  # Enable DNS rebinding protection and other `Host` header attacks.
  config.hosts = %w[admin upskill api partners learning-delivery].map { |host| "#{host}.#{ENV.fetch('STAGING_SERVER_NAME', nil)}.stg.ziplines.dev" }
  config.host_authorization = { exclude: ->(request) { request.path =~ %r{^/up$} } }

  config.action_mailer.default_url_options = {
    host: "upskill.#{ENV.fetch('STAGING_SERVER_NAME', 'dev')}.stg.ziplines.dev",
  }

  ActionMailer::Base.smtp_settings = {
    address: 'smtp.sendgrid.net',
    port: '587',
    authentication: :plain,
    user_name: 'apikey',
    password: Rails.application.credentials.SENDGRID_ACCESS_KEY,
    domain: 'ziplines.com',
    enable_starttls_auto: true,
  }

  # Used to generate URLs for ActiveStorage attachments
  config.x.storage.host = "https://upskill.#{ENV.fetch('STAGING_SERVER_NAME', nil)}.stg.ziplines.dev"

  config.x.admin.host = "admin.#{ENV.fetch('STAGING_SERVER_NAME', nil)}.stg.ziplines.dev"
end
