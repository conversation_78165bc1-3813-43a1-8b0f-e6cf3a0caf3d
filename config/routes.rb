# frozen_string_literal: true

Rails.application.routes.draw do
  constraints ->(req) { ['admin', 'learning-delivery'].include?(req.subdomain) } do
    devise_for :admin_users, controllers: { omniauth_callbacks: 'admin_users/omniauth_callbacks' }
    devise_scope :admin_user do
      get 'admin_users/sign_in', to: 'admin_users/sessions#new', as: :new_admin_user_session
      get 'admin_users/sign_out', to: 'admin_users/sessions#destroy', as: :destroy_admin_user_session
    end

    namespace :admin_users do
      get 'home', to: 'home#index'
    end

    root to: "admin_users/home#index"
  end

  constraints subdomain: /^admin/ do
    ActiveAdmin.routes(self)

    authenticate :admin_user, ->(u) { u.permissions.exists?(level: [:r, :rw, :rwd], resource_group: 'Development') } do
      mount Sidekiq::Web, at: '/sidekiq'
    end
  end

  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  post 'api/v1/stripe_webhook', to: 'payments/stripe/webhook#create', as: :stripe_webhook
  post 'api/v1/hubspot_webhook', to: 'remote/hubspot/webhook#create', as: :hubspot_webhook

  # constraints subdomain: /^api/ do
  scope module: 'api' do
    namespace :v1 do
      namespace :referral_rock do
        resources :members, only: :create
      end
    end
  end
  # end

  constraints subdomain: /^api/ do
    scope module: 'remote' do
      namespace :canvas do
        resource :data, only: [] do
          match '/data', to: 'data#data', via: [:get, :post]
          match '/course-pages/:course_id', to: 'data#course_pages', via: [:get, :post]

          post '/course-pages-flush', to: 'data#flush_cache'
          get '/user/:user_id', to: 'data#user_info'
          get '/user/:user_id/course/:course_id/assignment/:assignment_id', to: 'data#assignment_submission'
        end
      end
    end
  end

  # When using DEV_SITE_TUNNEL_HOSTNAME, the subdomain is empty since the tld_length is 2 in development and the tunnel host_name it is 1.
  # This causes Rails to chop off the subdomain and use empty string for subdomain... which we will use the 'site' module below.
  subdomain = Rails.env.development? && ENV['DEV_SITE_TUNNEL_HOSTNAME'].present? ? '' : /^upskill/
  constraints subdomain: do
    get 'sitemap.xml', to: 'sitemaps#show', format: :xml, as: :sitemap

    # Used by partner proxy (proxy-servers) automated tests to verify that the proxy is configured correctly
    # Available in all environments except production
    match 'proxy_test', to: 'proxy_test#index', via: [:get, :post], as: :proxy_test

    scope module: 'remote' do
      namespace :canvas do
        resources :password_resets, only: [:new, :edit, :create, :update], as: :password_resets
      end
    end

    scope module: 'site' do
      get ':program_slug/payment', to: 'payments#new'
      get '/payments/new', as: :new_site_payments
      get '/payments/create', as: :site_payments
      get '/payment/:order_id/confirmation', to: 'payments#confirmation', as: :site_payment_confirmation
      get '/payment/:order_id/canceled', to: 'payments#canceled', as: :site_payment_canceled

      # Order confirmation routes
      get '/order/:order_id/confirm', to: 'orders#confirm', as: :site_order_confirm
      patch '/order/:order_id/confirm', to: 'orders#confirm_update', as: :site_order_confirm_update

      post 'payment_intents', to: 'payments/stripe/payment_intents#create', as: :site_payment_intents
      post '/payments/affirm/:order_id/confirmation', to: 'payments/affirm/confirmation#create', as: :site_payment_affirm_confirmation

      get 'referral_landing/:referral_code', to: 'referral_landing#show', as: :referral_landing

      get 'privacy-policy', to: redirect('https://www.ziplines.com/privacy-policy')
      get 'end-user-agreement', to: redirect('https://www.ziplines.com/end-user-agreement')

      constraints(Constraints::PartnerProxyHost.new) do
        get 'get-to-know-you', to: 'get_to_know_you#show', as: :proxy_host_site_get_to_know_you

        get ':program_slug/registrations/new', to: 'registrations#new', as: :proxy_host_new_site_registrations
        get ':program_slug_with_enrollment', to: 'registrations#redirect', constraints: { program_slug_with_enrollment: /[\w\.\-_]+-enrollment/ }

        get ':program_slug/syllabus', to: 'syllabus#show', as: :proxy_host_site_syllabus
        get ':program_abbreviation_with_syllabus', to: 'syllabus#redirect', constraints: { program_abbreviation_with_syllabus: /[\w\.\-_]+-syllabus/ }

        get ':program_slug/course', to: 'squeeze#show', as: :proxy_host_site_squeeze
        get ':program_slug_with_course', to: 'squeeze#redirect', constraints: { program_slug_with_course: /[\w\.\-_]+-course/ }

        get ':program_slug/reimbursement', to: 'reimbursement#show', as: :proxy_host_site_reimbursement
        get ':program_abbreviation_with_reimbursement', to: 'reimbursement#redirect',
          constraints: { program_abbreviation_with_reimbursement: /[\w\.\-_]+-reimbursement/ }

        get '(:program_slug)', to: 'landing#show', as: :proxy_host_site_landing
        resources :syllabus_requests, only: :create, as: :proxy_host_create_site_syllabus_requests
        resources :registrations, only: :create, as: :proxy_host_create_site_registrations
      end

      constraints partner_identifier: /[\w\.\-_]+/ do
        get ':partner_identifier/get-to-know-you', to: 'get_to_know_you#show', as: :site_get_to_know_you

        get ':partner_identifier/:program_slug/registrations/new', to: 'registrations#new', as: :new_site_registrations
        get ':partner_identifier/:program_slug_with_enrollment', to: 'registrations#redirect',
          constraints: { program_slug_with_enrollment: /[\w\.\-_]+-enrollment/ }

        get ':partner_identifier/:program_slug/syllabus', to: 'syllabus#show', as: :site_syllabus
        get ':partner_identifier/:program_abbreviation_with_syllabus', to: 'syllabus#redirect',
          constraints: { program_abbreviation_with_syllabus: /[\w\.\-_]+-syllabus/ }

        get ':partner_identifier/:program_slug/course', to: 'squeeze#show', as: :site_squeeze
        get ':partner_identifier/:program_slug_with_course', to: 'squeeze#redirect', constraints: { program_slug_with_course: /[\w\.\-_]+-course/ }

        get ':partner_identifier/:program_slug/reimbursement', to: 'reimbursement#show', as: :site_reimbursement
        get ':partner_identifier/:program_abbreviation_with_reimbursement', to: 'reimbursement#redirect',
          constraints: { program_abbreviation_with_reimbursement: /[\w\.\-_]+-reimbursement/ }

        get ':partner_identifier(/:program_slug)', to: 'landing#show', as: :site_landing

        resources :syllabus_requests, only: :create, as: :create_site_syllabus_requests
        resources :registrations, only: :create, as: :create_site_registrations
      end

      # Redirect unknown partners / invalid urls to ziplines, but do not redirect active_storage urls
      get '*path', to: redirect('http://www.ziplines.com'), constraints: lambda { |req|
        req.path.exclude?('rails/active_storage')
      }
      get '', to: redirect('https://www.ziplines.com'), as: :site_root
    end
  end

  constraints subdomain: /^partners/ do
    scope module: 'partners', as: 'partners' do
      scope ':partner_uid/' do
        resource :dns_status, only: :show
      end
    end
  end

  constraints subdomain: /^learning-delivery/ do
    scope module: 'learning_delivery', as: 'learning_delivery' do
      get '/profile', to: 'employees#show', as: 'employee_profile'
      get '/profile/availability', to: 'employee/availabilities#edit'
      resources :employees, only: [:show] do
        scope module: 'employee' do
          resource :availability, only: [:edit, :update]
        end
      end
      resources :schedules, only: [:index, :create]
      resources :submissions, only: [:index, :show] do
        post 'publish', on: :member

        namespace :submission, path: '' do
          resources :exercise_reviews, only: [:update]
          resources :reviews, only: [:update]
          resources :comments, only: [:create, :destroy] do
            collection do
              patch :mark_as_read
            end
          end
        end
      end
    end
  end

  get 'welcome', to: 'welcome#index'

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get 'up' => 'rails/health#show', as: :rails_health_check

  resources :schema_migrations, only: :index

  # Defines the root path route ("/")
  # root "posts#index"
end
