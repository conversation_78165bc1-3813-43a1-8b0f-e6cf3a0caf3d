# frozen_string_literal: true

module LearningDelivery
  class SideNavbarPresenter < ApplicationPresenter
    attr_reader :admin_user

    def initialize(admin_user)
      @admin_user = admin_user
    end

    def employee
      @employee ||= admin_user.learning_delivery_employee
    end

    def to_partial_path
      'learning_delivery/side_navbar'
    end

    def active_sections
      return [] unless employee

      employee.sections.select(&:active?)
    end

    def active_admin_path
      return unless admin_user.admin?

      UrlBuilder::Admin.root_url
    end

    def section_title(section)
      program_abbreviation = section.cohort.program.abbreviation
      "#{program_abbreviation} #{section.cohort.starts_on.strftime("%b '%y")} Section #{section.suffix}"
    end
  end
end
