# frozen_string_literal: true

module LearningDelivery
  class Task
    class RemoveLearnerPresenter < BasePresenter
      VARIABLES = %w[
        {SECTION_INFO}
        {LEARNER_NAME}
        {LEARNER_EMAIL}
        {PROFILE_LINK}
      ].freeze

      attr_reader :resource

      alias_method :enrollment, :resource

      delegate :learner, :section, to: :enrollment

      def initialize(resource:)
        super
        @resource = resource
        validate_resource_type!
      end

      def task_template
        @task_template ||= TaskTemplate.find_by!(task_type: :remove_learner, sub_type: nil)
      end

      private

      def validate_resource_type!
        return if resource.is_a?(Enrollment)

        raise ArgumentError.new("Expected Enrollment, got #{resource.class}")
      end

      def placeholder_values
        @placeholder_values ||= {
          '{LEARNER_NAME}' => :learner_name,
          '{LEARNER_EMAIL}' => :learner_email,
          '{SECTION_INFO}' => :section_info,
          '{PROFILE_LINK}' => :profile_link,
        }
      end

      def learner_name
        @learner_name ||= learner.full_name
      end

      def learner_email
        @learner_email ||= learner.primary_email_address
      end

      def profile_link
        @profile_link ||= begin
          <<~PROFILE_LINK
 <a href="#{enrollment.remote_canvas_user.remote_link}" target="_blank"><span>Link to Profile</span></a>
          PROFILE_LINK
        end
      end
    end
  end
end
