# frozen_string_literal: true

module LearningDelivery
  module Employees
    class AccordionPresenter < ApplicationPresenter
      attr_reader :sections

      def initialize(employee)
        @sections = employee.sections
      end

      def live_day_and_time(section)
        Section::LiveDayTimeFormatter.new(section:).to_fs(format: :short)
      end

      def enrollments_at_high_risk_count(section)
        section.enrollments.retained.high_risk.size
      end

      def enrollments_count(section)
        section.enrollments.retained.size
      end

      def enrollments_percentage(section)
        total_enrollments = enrollments_count(section).to_f
        return 0 if total_enrollments.zero?

        ((enrollments_at_high_risk_count(section) / total_enrollments) * 100).round
      end

      def format_section_title(section)
        program_abbreviation = section.cohort.program.abbreviation
        "#{program_abbreviation} #{section.cohort.starts_on.strftime("%b '%y")} Section #{section.suffix}"
      end

      def week_number(section)
        section.cohort.week_number
      end

      def conferencing_url(section)
        section.conferencing_url
      end

      def to_partial_path
        'learning_delivery/employees/accordion'
      end
    end
  end
end
