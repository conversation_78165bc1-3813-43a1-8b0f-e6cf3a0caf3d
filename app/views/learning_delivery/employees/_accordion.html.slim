
#accordion-collapse.rounded-t-lg.bg-bg-secondary-grey-zip
  div[data-controller='toggle'data-toggle-open-value="true"]
    .bg-white.p-3.rounded-lg.border.border-tertiary-light-grey-zip.shadow-md
      div[data-action='click->toggle#toggle touch->toggle#toggle']
        h2#accordion-collapse-heading-1.bg-white
          button.flex.items-center.justify-between.w-full.p-2.pl-0.font-medium.rtl:text-right.text-gray-500.rounded-t-xl.bg-white.gap-3.bg-white[type="button"  aria-expanded="true" aria-controls="accordion-collapse-body-1" ]
            span.text-blacktext.text-base.font-semibold.pl-1
              | Active Sections
            svg.bg-tertiary-light-grey-zip.w-6.h-6.p-2.rounded-full.rotate-180.shrink-0[ xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6"]
              path[stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 1 5 5 9 1"]
      .[data-toggle-target='toggleable']
        #accordion-collapse-body-1.leave-to.enter-to[data-toggle-target='toggleable' aria-labelledby="accordion-collapse-heading-1"]
        - if presenter.sections.any?(&:active?)
            - presenter.sections.select(&:active?).each do |section|
              .border.border-tertiary-light-grey-zip.rounded-lg.bg-white.mb-4
                .sm:flex.justify-between.block.sm:mt-0.mt-2.bg-bg-secondary-grey-zip.p-3
                  h6.text-blacktext.text-base.font-semibold.mb-0
                    | #{presenter.format_section_title(section)}
                    button.text-green-zip.font-normal.bg-tertiary-light-grey-zip.px-3.py-1.rounded-full.text-sm.ml-1
                      | Started
                  p.flex.gap-2.sm:mt-0.mt-2
                    = svg(:signal, class: 'w-6 h-6 text-orange-600')
                    | Live at #{presenter.live_day_and_time(section)}
                .sm:flex.justify-between.mt-3.block.p-3
                  .sm:flex-nowrap.flex.flex-wrap.gap-1.items-center
                    span.text-extra-dark-grey-zip.text-base.font-semibold.ml-2
                      | Week: #{presenter.week_number(section)}
                .grid.sm:grid-cols-3.grid-cols-1.lg:gap-4.gap-2.p-3
                  .bg-extra-light-yellow-zip.rounded-xl
                    .flex.lg:gap-6.iteams-center.relative.gap-1.xl:py-4.xl:px-6.px-2.py-4
                      .chart-progress.text-dark-yellow-zip.font-semibold[role="progressbar" aria-valuenow="#{presenter.enrollments_percentage(section)}" aria-valuemin="0" aria-valuemax="100" style="--value:#{presenter.enrollments_percentage(section)}"]
                        | #{presenter.enrollments_percentage(section)}
                        span.text-base.pt-2
                          |%
                      div
                        p.text-extra-dark-grey-zip.text.font-normal.text-xs.pb-1
                          | Total
                        h2.text-extra-dark-grey-zip.font-semibold.md:text-2xl.pb-1.text-lg.border-b.border-b-light-yellow-zip.pb-2 #{presenter.enrollments_count(section)}
                        p.text-secondary-zip.text-xs.font-normal
                          | At Risk
                        h2.text-extra-dark-grey-zip.font-semibold.md:text-2xl.pb-1.text-lg.pt-2 #{presenter.enrollments_at_high_risk_count(section)}
                    .flex.bg-secondary-dark-yellow-zip.justify-between.xl:py-4.xl:px-6.px-2.py-4.rounded-bl-xl.rounded-br-xl
                      h4.text-dark-yellow-zip.md:text-xl.text-sm.font-semibold.flex.gap-1
                        = image_tag('learning_delivery/icons/learning.svg')
                        | Learners
                      a
                      i.fa-solid.fa-arrow-right
                  .bg-tertiary-light-grey-zip.rounded-xl.opacity-75
                    .flex.lg:gap-4.iteams-center.relative.xl:py-4.xl:px-6.gap-1.py-3.px-2
                      .chart-progress1.text-purple-zip.font-semibold[role="progressbar" aria-valuenow="90" aria-valuemin="0" aria-valuemax="100" style="--value:90"]
                        |120
                        span.text-base.pt-2
                          |%
                      div
                        p.text-extra-dark-grey-zip.text.font-normal.text-xs.pb-1
                          | Total
                        h2.text-extra-dark-grey-zip.font-semibold.md:text-2xl.pb-1.text-lg.border-b.border-b-light-yellow-zip.pb-2
                          |  160
                        p.text-secondary-zip.text-xs.font-normal
                          | At Risk
                        h2.text-extra-dark-grey-zip.font-semibold.md:text-2xl.pb-1.text-lg.pt-2
                          |  120
                    .flex.bg-secondary-dark-Purple-zip.justify-between.opacity-1.xl:py-4.xl:px-6.py-3.px-4.rounded-bl-xl.rounded-br-xl
                      h4.md:text-xl.text-sm.font-semibold.text-purple-zip.flex.gap-1
                        = image_tag('learning_delivery/icons/open-book.svg')
                        | Assignments
                      a
                      i.fa-solid.fa-arrow-right
                  .bg-tertiary-light-grey-zip.rounded-xl
                    .flex.lg:gap-6.iteams-center.relative.gap-1.xl:py-4.xl:px-6.opacity-75.px-2.py-4
                      .chart-progress2.text-dark-green-zip.font-semibold[role="progressbar" aria-valuenow="90" aria-valuemin="0" aria-valuemax="100" style="--value:90"]
                        |40
                        span.text-base.pt-2
                          |%
                      div
                        p.text-extra-dark-grey-zip.text.font-normal.text-xs.pb-1
                          | Total
                        h2.text-extra-dark-grey-zip.font-semibold.md:text-2xl.pb-1.text-lg.border-b.border-b-light-yellow-zip.pb-2
                          |  60
                        p.text-secondary-zip.text-xs.font-normal
                          | At Risk
                        h2.text-extra-dark-grey-zip.font-semibold.md:text-2xl.pb-1.text-lg.pt-2
                          |  40
                    .flex.bg-primary-light-green-zip.justify-between.xl:py-4.xl:px-6.opacity-75.px-3.py-4.rounded-bl-xl.rounded-br-xl
                      h4.text-dark-green-zip.md:text-xl.text-sm.font-semibold.flex.gap-1
                        = image_tag('learning_delivery/icons/task-list.svg')
                        | Tasks
                      a
                      i.fa-solid.fa-arrow-right

