# frozen_string_literal: true

h3 "Task Details"

attributes_table_for resource do
  row :uid
  row :title
  row(:type) { |task| task.type.demodulize if task.type.present? }
  row :sub_type
  row(:status)
  row :owner
  row :assigned_by
  row :due_at
  row :created_at
end

hr

panel "Add Comment" do
  active_admin_form_for(comment, url: create_comment_admin_learning_delivery_task_path(resource), method: :post) do |f|
    f.semantic_errors

    inputs do
      input :text, as: :text, label: "Comment", required: true, placeholder: "Enter your comment here", input_html: { rows: 10 }
    end

    actions do
      f.action :submit, label: 'Add Comment'
      cancel_link
    end
  end
end

hr

panel "Previous Comments" do
  persisted_comments = resource.comments.where.not(id: nil).includes(:author)
  if persisted_comments.any?
    table_for persisted_comments.newest_first do
      column :text do |comment|
        simple_format comment.text
      end
      column :author do |comment|
        link_to comment.author.full_name, admin_admin_user_path(comment.author)
      end
      column "Role" do |comment|
        comment.author.learning_delivery_employee&.role_names || 'Admin'
      end
      column :created_at do |comment|
        comment.created_at.strftime('%b %d, %Y at %I:%M %p')
      end
    end
  else
    div class: "blank_slate_container" do
      span class: "blank_slate" do
        span "No comments yet"
      end
    end
  end
end
