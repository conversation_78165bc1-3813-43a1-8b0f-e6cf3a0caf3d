# frozen_string_literal: true

panel "Manual Open Cohort" do
  active_admin_form_for(:manual_open, url: manual_open_admin_cohort_path(resource), method: :post) do |f|
    inputs do
      input :cohort_id, as: :hidden
      input :notify_in, as: :time_select, label: 'Notify in', hint: '⚠️(HH:MM) Leave blank to notify immediately.'
    end
    actions do
      f.action :submit, label: 'Submit'
      cancel_link
    end
  end
end

hr

panel 'Cohort' do
  attributes_table_for(resource) do
    row(:id)
    row(:program)
    row(:program_duration) { |c| "#{c.program.duration_in_weeks} weeks" }
    row(:name) { |c| copyable(c.name) }
    row(:key) { |c| copyable(c.key) }
    row(:status) { |c| status_tag(c.status_name) }
    row(:add_period_active?)
    row(:add_period_starts_on) { |c| date_icon(c.add_period_starts_on) + date_with_diff(c.add_period_starts_on, diff_from: c.starts_on, interval: 'month') }
    row(:lms_opens_on) { |c| date_icon(c.lms_opens_on) + date_with_diff(c.lms_opens_on, diff_from: c.starts_on) }
    row(:starts_on) { |c| date_icon(c.starts_on) + c.starts_on.to_fs(:long) }
    row(:week_number)
    row(:add_period_ends_on) { |c| date_icon(c.add_period_ends_on) + date_with_diff(c.add_period_ends_on, diff_from: c.starts_on) }
    row(:drop_period_ends_on) { |c| date_icon(c.drop_period_ends_on) + date_with_diff(c.drop_period_ends_on, diff_from: c.starts_on) }
    row(:transfer_period_ends_on) { |c| date_icon(c.transfer_period_ends_on) + date_with_diff(c.transfer_period_ends_on, diff_from: c.starts_on) }
    row(:ends_on) { |c| date_icon(c.ends_on) + date_with_diff(c.ends_on, diff_from: c.starts_on) }
    row(:extension_period_ends_on) { |c| date_icon(c.extension_period_ends_on) + date_with_diff(c.extension_period_ends_on, diff_from: c.starts_on) }
    row(:lms_closes_on) { |c| date_icon(c.lms_closes_on) + date_with_diff(c.lms_closes_on, diff_from: c.starts_on, interval: 'year') }
  end
end
