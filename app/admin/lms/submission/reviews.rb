# frozen_string_literal: true

ActiveAdmin.register Lms::Submission::Review do
  menu parent: ['LMS', 'Grading'], label: 'Submission Reviews'

  actions :index, :show

  includes :submission, :manually_reviewed_by, :enrollment, :learner, :assignment, :exercise_reviews, :training_evaluation, section: [:cohort, :program]

  scope(:all, default: true, group: :training_group)
  scope('🏋️ Training', :training, group: :training_group)
  scope('⚡ Live', :live, group: :training_group)

  live_scopes = %w[ live accepted overridden ai_skipped].freeze

  scope('✅ Accepted', :auto_grader_accepted, group: :grade_group, if: proc { live_scopes.include?(params.dig(:scope)) })
  scope('🔄 Overridden', :auto_grader_overridden, group: :grade_group, if: proc { live_scopes.include?(params.dig(:scope)) })
  scope('❓ AI Skipped', :auto_grader_blank, group: :grade_group, if: proc { live_scopes.include?(params.dig(:scope)) })


  training_scopes =
    %w[ training  ] + Lms::Submission::Review::TrainingEvaluation.statuses.keys + Lms::Submission::Review::TrainingEvaluation.results.keys
  Lms::Submission::Review::TrainingEvaluation.status_names.each do |status, name|
    scope(name, group: :training_status, if: proc { training_scopes.include?(params.dig(:scope)) }) do |scope|
      scope.joins(:training_evaluation).where(lms_submission_review_training_evaluations: { status: })
    end
  end

  Lms::Submission::Review::TrainingEvaluation.result_names.each do |result, name|
    scope(name, group: :training_result, if: proc { training_scopes.include?(params.dig(:scope)) }) do |scope|
      scope.joins(:training_evaluation).where(lms_submission_review_training_evaluations: { result: })
    end
  end

  filter :uid_cont, label: 'UID'
  filter :state, as: :select, collection: Lms::Submission::Review.state_alt_name_to_ids
  filter :grade, as: :select, collection: Lms::Submission::Review.grade_alt_name_to_ids
  filter :auto_grader_grade, as: :select, collection: Lms::Submission::Review.auto_grader_grade_alt_name_to_ids
  filter :cohort_id,
    as: :search_select_filter,
    label: 'Cohort',
    url: proc { admin_cohorts_path },
    fields: %w[name key],
    display_name: 'name',
    order_by: :name
  filter :program, as: :select, collection: -> { Program.order(:name).pluck(:name, :id) }
  filter :grading_config_id,
    as: :search_select_filter,
    label: 'Grading Config',
    url: proc { admin_lms_grading_configs_path },
    fields: ['uid'],
    display_name: 'humanized_uid',
    order_by: :id
  filter :assignment_template_id,
    as: :search_select_filter,
    label: 'Assignment Template',
    url: proc { admin_lms_assignment_templates_path },
    fields: ['name'],
    display_name: 'full_name',
    order_by: :name
  filter :attempt, as: :numeric_range_filter
  filter :pdf_generated_at, as: :date_time_picker_filter
  filter :auto_graded_at, as: :date_time_picker_filter
  filter :manually_reviewed_at, as: :date_time_picker_filter
  filter :graded_at, as: :date_time_picker_filter
  filter :published_at, as: :date_time_picker_filter
  filter :created_at, as: :date_time_picker_filter
  filter :training, as: :boolean, label: 'Training Review'
  filter :mismatched_grades, as: :boolean, label: 'Mismatched Grades'
  filter :training_evaluation_status,
    as: :select,
    label: 'Training Evaluation Status',
    collection: -> { Lms::Submission::Review::TrainingEvaluation.status_alt_name_to_ids }
  filter :training_evaluation_result,
    as: :select,
    label: 'Training Evaluation Result',
    collection: -> { Lms::Submission::Review::TrainingEvaluation.result_alt_name_to_ids }

  index do
    if params.dig(:scope) == 'training'
      panel "Training Evaluation Results" do
        filtered_scope = Lms::Submission::Review.training.ransack(params[:q]).result.joins(:training_evaluation)
        results = filtered_scope.where.not(lms_submission_review_training_evaluations: { result: nil })
          .group('lms_submission_review_training_evaluations.result')
          .count
          .transform_keys { |k| Lms::Submission::Review::TrainingEvaluation.result_names[k.to_s] || k }

        # Get status data for progress bars
        status_results = filtered_scope
          .group('lms_submission_review_training_evaluations.status')
          .count
          .transform_keys { |k| Lms::Submission::Review::TrainingEvaluation.status_names[k.to_s] || k }

        status_total = status_results.values.sum

        color_mapping = {
          'Correct' => '#3d8427',
          'Incorrect' => '#d53e4f',
          'Manual Review' => '#3288bd',
          nil => '#7f8b92',
        }

        status_color_mapping = {
          'Created' => '#7f8b92',      # Light gray blue
          'Evaluated' => '#3d8427',    # Dark green
          'Auto Evaluated' => '#3288bd', # Light blue
          nil => '#7f8b92',
        }

        # Main container with side-by-side layout
        div(class: 'flex items-start gap-7') do
          # LEFT SIDE: Status progress bar section
          div class: "bg-gray-50 p-5 rounded-lg shadow-sm flex-1" do
            h3 "Status Breakdown", class: "mt-0 mb-4 text-lg text-gray-800 font-medium"

            ordered_statuses = ['Evaluated', 'Auto Evaluated', 'Created']
            ordered_results = ordered_statuses.index_with do |status|
              status_results[status] || 0
            end

            percentages = {}
            ordered_results.each do |status, count|
              percentages[status] = (count.to_f / status_total * 100).round(1)
            end

            div class: "mb-6" do
              div class: "w-full bg-gray-200 rounded-full h-5 overflow-hidden" do
                div class: "relative h-full w-full" do
                  current_position = 0

                  ordered_results.each_key do |status|
                    percentage = percentages[status]
                    color = status_color_mapping[status] || '#7f8b92'
                    next if percentage == 0

                    div class: "absolute h-full", style: "left: #{current_position}%; width: #{percentage}%; background-color: #{color};" do
                      ''
                    end

                    current_position += percentage
                  end
                end
              end

              # Legend and counts
              div class: "flex flex-wrap mt-3 gap-x-6 gap-y-2" do
                ordered_results.each do |status, count|
                  percentage = percentages[status]
                  color = status_color_mapping[status] || '#7f8b92'

                  div class: "flex items-center" do
                    # Color indicator
                    span class: "inline-block w-3 h-3 mr-2 rounded", style: "background-color: #{color};"

                    # Status name and count
                    span class: "font-medium text-gray-800 pr-1" do
                      text_node "#{status}: "
                    end
                    span class: "text-gray-600" do
                      text_node "#{count} (#{percentage}%)"
                    end
                  end
                end
              end
            end

            div class: "mt-4 pt-4 border-t border-gray-300" do
              span class: "font-semibold text-gray-800" do
                text_node "Total: #{status_total} reviews"
              end
            end
          end

          # RIGHT SIDE: Results visualization section - Pie chart and breakdown in same card
          div(class: 'flex-1') do
            div class: "bg-gray-50 p-5 rounded-lg shadow-sm" do
              total = results.values.sum

              h3 "Results Breakdown", class: "mt-0 mb-4 text-lg text-gray-800 font-medium"

              div(class: 'flex') do
                # Pie chart inside the card
                div class: "mb-6" do
                  colors = results.keys.map { |key| color_mapping[key] || color_mapping[nil] }
                  pie_chart(results, donut: true, colors:, height: "15em", legend: { display: false })
                end

                # Results breakdown below the chart
                div do
                  results.each do |key, value|
                    percentage = (value.to_f / total * 100).round(1)
                    color = color_mapping[key] || '#7f8b92'

                    div class: "flex items-center mb-3" do
                      span class: "inline-block w-4 h-4 mr-2.5 rounded", style: "background-color: #{color};"

                      span class: "flex-grow font-medium text-gray-800 pr-2" do
                        text_node (key || 'Unknown').to_s
                      end

                      span class: "font-semibold text-gray-800" do
                        text_node "#{percentage}% "
                        span class: "font-normal text-gray-600 text-sm" do
                          text_node "(#{value})"
                        end
                      end
                    end
                  end

                  div class: "mt-4 pt-4 border-t border-gray-300" do
                    span class: "font-semibold text-gray-800" do
                      text_node "Total: #{total} reviews"
                    end
                  end
                end
              end
            end
          end
        end
      end
    end

    if live_scopes.include?(params.dig(:scope))
      panel "Auto Grader Breakdown" do
        filtered_scope = Lms::Submission::Review.ransack(params[:q]).result

        # Get auto grader data for pie chart
        auto_grader_results = {
          'Accepted' => filtered_scope.auto_grader_accepted.count,
          'Overridden' => filtered_scope.auto_grader_overridden.count,
          'AI Skipped' => filtered_scope.auto_grader_blank.count,
        }

        total = auto_grader_results.values.sum

        color_mapping = {
          'Accepted' => '#3d8427',    # Dark green
          'Overridden' => '#d53e4f',  # Red
          'AI Skipped' => '#3288bd',  # Blue
          nil => '#7f8b92',           # Gray
        }

        # Main container with side-by-side layout
        div(class: 'flex-1') do
          div class: "bg-gray-50 p-5 rounded-lg shadow-sm" do
            div(class: 'flex') do
              # Pie chart inside the card
              div class: "mb-6" do
                colors = auto_grader_results.keys.map { |key| color_mapping[key] || color_mapping[nil] }
                pie_chart(auto_grader_results, donut: true, colors:, height: "15em", legend: { display: false })
              end

              # Results breakdown below the chart
              div do
                auto_grader_results.each do |key, value|
                  percentage = total.zero? ? 0 : (value.to_f / total * 100).round(1)
                  color = color_mapping[key] || '#7f8b92'

                  div class: "flex items-center mb-3" do
                    span class: "inline-block w-4 h-4 mr-2.5 rounded", style: "background-color: #{color};"

                    span class: "flex-grow font-medium text-gray-800 pr-2" do
                      text_node (key || 'Unknown').to_s
                    end

                    span class: "font-semibold text-gray-800" do
                      text_node "#{percentage}% "
                      span class: "font-normal text-gray-600 text-sm" do
                        text_node "(#{value})"
                      end
                    end
                  end
                end

                div class: "mt-4 pt-4 border-t border-gray-300" do
                  span class: "font-semibold text-gray-800" do
                    text_node "Total: #{total} reviews"
                  end
                end
              end
            end
          end
        end
      end
    end

    column(:uid) do |r|
      training_icon = r.training ? "🏋️" : "⚡"
      copyable(link_to([training_icon, r.humanized_uid].compact.join(' '), admin_lms_submission_review_path(r)), r.humanized_uid)
    end
    stack(
      state: :state_html,
      submission_grade: proc { |r| "<span class='status_tag #{r.submission.grade.parameterize}'>#{r.submission.grade}</span>" },
    )
    stack(
      auto_grader_grade: :auto_grader_grade_html,
      grade: :grade_html,
    )
    stack(
      learner: proc { |r| link_to(r.learner.full_name, admin_learner_path(r.learner)) },
      attempt: :attempt,
    )
    stack(
      section: proc { |r| display_section(r.section) },
      assignment: proc { |r| "Week #{r.cohort_week&.number}: #{link_to(r.assignment.name, admin_lms_assignment_path(r.assignment))}" },
    )
    column :attempt
    column "Exercise Reviews" do |r|
      r.exercise_reviews.sort_by { |er| [er.exercise_config&.order.to_i, er.exercise_config&.id.to_i] }.map do |er|
        grade_html = er.grade_html

        state_icons = {
          'submitted' => "<i class='fas fa-clock' title='Submitted'></i>",
          'auto_graded' => "<i class='fas fa-robot' title='Auto Graded'></i>",
          'manual_review_needed' => "<i class='fas fa-user-edit' title='Manual Review Needed'></i>",
          'graded' => "<i class='fas fa-check-circle' title='Graded'></i>",
        }
        state_icon = state_icons[er.state] || ""

        confidence_icons = {
          'high' => "<i class='fas fa-thermometer-full' title='High Confidence'></i>",
          'medium' => "<i class='fas fa-thermometer-half' title='Medium Confidence'></i>",
          'low' => "<i class='fas fa-thermometer-empty' title='Low Confidence'></i>",
        }
        confidence_icon = confidence_icons[er.autograder_confidence] || ""

        "<div class='exercise-review-container'>
          <span class='exercise-review-icons'>#{state_icon} #{confidence_icon}</span>
          #{grade_html}
        </div>"
      end.join.html_safe
    end
    stack(
      'Training Status': proc { |r| r&.training_evaluation&.status_html },
      'Training Result': proc { |r| r&.training_evaluation&.result_html },
    )
    column :auto_graded_at
    column :manually_reviewed_at
    column :manually_reviewed_by
    column :graded_at
    column(:created_at) { |r| r.created_at.to_fs(:datetime_with_zone) }
  end

  show do
    render(partial: 'admin/lms/submissions/reviews/show', locals: { review: resource })
  end

  action_item :retrain, only: :show, if: proc { resource.training? } do
    link_to 'Retrain', retrain_admin_lms_submission_review_path(resource),
      class: 'button',
      method: :post,
      data: { confirm: 'Are you sure you want to retrain this submission?' }
  end

  member_action :retrain, method: :post do
    review = Lms::Submission::Review.find_from_uid!(params[:id])
    if review.training?
      Lms::Submission::AutoGradeJob.perform_async(review.submission_id, true)
      redirect_to admin_lms_submission_review_path(review), notice: 'Retraining job has been enqueued'
    else
      redirect_to admin_lms_submission_review_path(review), alert: 'This review is not marked for training'
    end
  end

  member_action :update_training_evaluation, method: :post do
    review = Lms::Submission::Review.find_from_uid!(params[:id])
    training_evaluation = review.training_evaluation

    if review.training? && training_evaluation.present?
      params_hash = params.require(:lms_submission_review_training_evaluation).permit(:result, :notes).to_h

      Lms::Submission::Review::TrainingEvaluation::EvaluateCommand.call!(
        training_evaluation:,
        result: params_hash[:result],
        admin_user: current_admin_user,
        notes: params_hash[:notes],
      )

      redirect_to admin_lms_submission_review_path(review), notice: 'Training evaluation updated successfully'
    else
      redirect_to admin_lms_submission_review_path(review), alert: 'Cannot update training evaluation'
    end
  end

  controller do
    def action_to_permission(action)
      case action
      when 'retrain', 'create_training_evaluation', 'update_training_evaluation'
        :update
      else
        super
      end
    end

    # Make the scoped collection available without pagination
    def scoped_collection_without_pagination
      end_of_association_chain.except(:limit, :offset)
    end

    # Make the method available to the view
    helper_method :scoped_collection_without_pagination
  end
end
