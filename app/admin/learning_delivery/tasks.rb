# frozen_string_literal: true

ActiveAdmin.register LearningDelivery::Task do
  menu parent: 'Learning Delivery', label: 'Tasks'

  actions :index, :show

  includes :resource, :owner, :assigned_by

  filter :uid_cont, label: 'UID'
  filter :title_cont, label: 'Title'
  filter :status, as: :select, collection: -> { LearningDelivery::Task.status_alt_name_to_ids }
  filter :sub_type, as: :select, collection: proc { LearningDelivery::Task.distinct.pluck(:sub_type).compact }
  filter :type, as: :select, collection: proc { LearningDelivery::Task.distinct.pluck(:type).compact.map { |t| [t.demodulize, t] } }
  filter :created_at, as: :date_time_picker_filter
  filter :due_at, as: :date_time_picker_filter
  filter :completed_at, as: :date_time_picker_filter

  index do
    column :id
    column(:uid) { |task| link_to(task.uid, admin_learning_delivery_task_path(task)) }
    column :title
    column(:type) { |task| task.type.demodulize if task.type.present? }
    column :sub_type
    column(:status, &:status_html)
    column :owner
    column :assigned_by
    column :due_at
    column :completed_at
    column :created_at
  end

  action_item :add_comment, only: :show do
    link_to('Add Comment', new_comment_admin_learning_delivery_task_path(resource)) if authorized?(:create, 'Learning Delivery')
  end

  show do
    attributes_table do
      row :id
      row :uid
      row :title
      row(:type) { |task| task.type.demodulize if task.type.present? }
      row :sub_type if resource.sub_type.present?
      row(:status, &:status_html)
      row :resource if resource.resource.present?
      row :description do |template|
        simple_format template.description
      end
      row :recommendation do |template|
        simple_format template.recommendation
      end
      row :reason do |template|
        simple_format template.reason
      end
      row :owner
      row :assigned_by
      row :assigned_at
      row :viewed_at
      row :due_at
      row :completed_at
      row :created_at
      row :updated_at
    end

    panel "Comments" do
      if resource.comments.any?
        table_for resource.comments.includes(:author).newest_first do
          column :text do |comment|
            simple_format comment.text
          end
          column :author do |comment|
            link_to comment.author.full_name, admin_admin_user_path(comment.author)
          end
          column "Roles" do |comment|
            comment.author.learning_delivery_employee&.role_names || 'Admin'
          end
          column :created_at do |comment|
            comment.created_at.strftime('%b %d, %Y at %I:%M %p')
          end
        end
      else
        div class: "blank_slate_container" do
          span class: "blank_slate" do
            span "No comments yet"
          end
        end
      end
    end
  end

  member_action :new_comment, method: :get do
    @comment = LearningDelivery::Task::Comment.new(task: resource)
    render 'admin/learning_delivery/tasks/new_comment'
  end

  member_action :create_comment, method: :post do
    comment_params = params.require(:learning_delivery_task_comment).permit(:text)
    LearningDelivery::Task::Comment::CreateCommand.call!(
      task: resource,
      author: current_admin_user,
      text: comment_params[:text],
    )
    redirect_to admin_learning_delivery_task_path(resource), notice: 'Comment was successfully added.'
  rescue ActiveRecord::RecordInvalid => e
    @comment = e.record
    render 'admin/learning_delivery/tasks/new_comment'
  end
end
