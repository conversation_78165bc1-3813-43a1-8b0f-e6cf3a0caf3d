# frozen_string_literal: true

module Remote
  module Hu<PERSON>pot
    class BatchPublishCommand < ApplicationCommand
      BATCH_LIMIT = 100 # Hubspot Batch API Limit

      attr_reader :collection, :payload, :client, :block

      # @param collection [Array<Remote::Hubspot::Contact, Remote::Hubspot::Deal, Remote::Hubspot::LineItem, Remote::Hubspot::Product>]
      #   The collection of resources to be batch published
      # @param payload [Hash, Proc, Symbol, nil] Hash - static payload, Proc/Symbol - dynamic payload, nil - default payload(dynamic)
      def initialize(collection:, payload: nil, &block)
        @collection = collection
        @payload = payload
        @client = HubspotClient.new
        @block = block
      end

      def call!
        return if collection.none?
        raise "Collection size exceeds the batch limit (#{BATCH_LIMIT})" if collection.size > BATCH_LIMIT

        client.post("/objects/#{remote_object_type}/batch/upsert", json: { inputs: }, &block)
      end

      private

      def inputs
        case payload
        when Hash # static payload
          collection.map { |resource| { **unique_identifier(resource), properties: payload['properties'] } }
        when Proc
          collection.map { |resource| { **unique_identifier(resource), properties: payload.call(resource)['properties'] } }
        when Symbol
          collection.map do |resource|
            { **unique_identifier(resource), properties: resource.public_send(payload)['properties'] }
          end
        else
          collection.map { |resource| { **unique_identifier(resource), properties: resource.default_payload['properties'] } }
        end.uniq do |input|
          input[:id]
        end
      end

      # @return ['contacts', 'deals', 'line_items', 'products']
      def remote_object_type
        collection.first.remote_object.pluralize
      end

      def unique_identifier(resource)
        { idProperty: resource.alternate_key_property, id: resource.alternate_key }
      end
    end
  end
end
