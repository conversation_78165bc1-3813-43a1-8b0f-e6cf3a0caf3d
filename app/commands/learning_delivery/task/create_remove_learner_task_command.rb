# frozen_string_literal: true

module LearningDelivery
  class Task
    class CreateRemoveLearnerTaskCommand < ApplicationCommand
      attr_reader :enrollment

      def self.callable?(enrollment:)
        new(enrollment:).callable?
      end

      def initialize(enrollment:)
        @enrollment = enrollment
      end

      def callable?
        no_open_tasks? &&
          Setting.value_for(:development_flags, :learning_delivery_tasks, :remove_learner).present?
      end

      def call!
        return unless callable?

        create_task
      end

      def create_task
        LearningDelivery::Task::RemoveLearner.create!(
          resource: enrollment,
          due_at: presenter.due_date,
          status: :created,
          title: presenter.title,
          description: presenter.description,
          recommendation: presenter.recommendation,
          reason: presenter.reason,
        )
      end

      def no_open_tasks?
        !LearningDelivery::Task::RemoveLearner.exists?(resource: enrollment, status: [:created, :assigned, :viewed])
      end

      def presenter
        @presenter ||= LearningDelivery::Task::RemoveLearnerPresenter.new(resource: enrollment)
      end
    end
  end
end
