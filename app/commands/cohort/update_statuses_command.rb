# frozen_string_literal: true

class Cohort
  class UpdateStatusesCommand < ApplicationCommand

    TIME_ZONE = 'Eastern Time (US & Canada)'

    attr_reader :scope, :types_to_update

    # @param type_to_update [Symbol] :starts or :ends.  What type of trigger to update statuses for.
    #  If :starts, will update things like lms_opens_on which we generally want to happen early in the "day" like at midnight.
    #  If :ends, will update things like ends_on which we generally want to happen at Cohort::CUTOVER_HOUR like 12am.
    #  If :all, will update both :starts and :ends triggers.
    #  (default: :all)
    def initialize(type_to_update: :all, scope: nil)
      @scope = scope || Cohort.all

      type_to_update = type_to_update.to_sym
      raise ArgumentError.new("Invalid type_to_update: #{type_to_update}") if %i[all starts ends].exclude?(type_to_update)

      @types_to_update = type_to_update == :all ? %i[starts ends] : [type_to_update]
    end

    # Given type_to_update :starts
    #   If lms_opens_on is June 3, we will update the status to opened on June 3 at the hour the cron runs (usually 1am est).
    # Given type_to_update :ends
    #   If ends_on is June 3, we will update the status to ended on June 4 at the hour the cron runs (usually 12am est).
    # The inactive status requires special handling, since it is not based on a static date attribute, so we handle that in a separate job.
    def call!
      Time.use_zone(TIME_ZONE) do
        mapping.each do |(status, statuses_not_to_update, date_attribute, next_date_attribute, type)|
          next if types_to_update.exclude?(type)

          date_threshold = type == :starts ? today : yesterday
          next_date_threshold = type == :starts ? tomorrow : today

          selected_cohorts = scope.where.not(status: statuses_not_to_update)
          selected_cohorts = selected_cohorts.where(date_attribute => ..date_threshold) if date_attribute
          selected_cohorts = selected_cohorts.where(next_date_attribute => next_date_threshold..) if next_date_attribute

          if status == :opened
            open_cohorts(selected_cohorts)
          else
            log(action: 'update_status', selected_cohort_ids: selected_cohorts.pluck(:id), status:)
            selected_cohorts.update!(status:)
          end
        end
      end
    end


    private

    # [
    #   [:created, [:created], nil, :lms_opens_on, :starts],
    #   [:opened, [:opened], :lms_opens_on, :starts_on, :starts],
    #   [:started, [:started], :starts_on, :ends_on, :starts],
    #   [:ended, [:ended], :ends_on, :extension_period_ends_on, :ends],
    #   [:extension_period_ended, [:extension_period_ended, :inactive], :extension_period_ends_on, :lms_closes_on, :ends],
    #   [:closed, [:closed], :lms_closes_on, nil, :ends]
    # ]
    def mapping
      status_date_trigger_pairs = STATUS_DATE_TRIGGERS.zip(STATUS_DATE_TRIGGERS[1..].pluck(:date_attribute))
      status_date_trigger_pairs.map do |status_date_trigger, next_date_attribute|
        status, date_attribute, type = status_date_trigger.values_at(:status, :date_attribute, :type)
        statuses_not_to_update = status == :extension_period_ended ? %i[ extension_period_ended inactive ] : [status]
        [status, statuses_not_to_update, date_attribute, next_date_attribute, type]
      end
    end

    def today
      @today ||= Time.zone.today
    end

    def yesterday
      @yesterday ||= today - 1
    end

    def tomorrow
      @tomorrow ||= today + 1
    end

    def open_cohorts(selected_cohorts)
      selected_cohorts.each do |cohort|
        OpenCommand.call!(
          cohort:,
        )
      end
    end

    def log(**args)
      JsonLogger.log(type: 'Cohort::UpdateStatusesCommand', **args)
    end
  end
end
