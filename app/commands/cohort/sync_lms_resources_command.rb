# frozen_string_literal: true

class Cohort
  class SyncLmsResourcesCommand < ApplicationCommand
    def call!
      sync_lms_resources_for_cohorts
      sync_lms_resources_for_sections
    end

    private

    def sync_lms_resources_for_cohorts
      cohorts.find_each do |cohort|
        begin
          Cohort::FetchModulesAndAssignmentGroupsCommand.call!(cohort:)
        rescue StandardError => e
          ErrorReporter.report(
            error: e,
            source: self.class.name,
            cohort_key: cohort.key,
            message: "Failed to refresh LMS data for cohort #{cohort.key}",
          )
        end
      end

      JsonLogger.log(
        type: 'Cohort::SyncLmsResourcesCommand',
        cohorts_ids: cohorts.ids,
      )
    end

    def sync_lms_resources_for_sections
      sections.find_each do |section|
        begin
          Section::AssignToAssignmentsCommand.call!(section:)
        rescue StandardError => e
          ErrorReporter.report(
            error: e,
            source: self.class.name,
            section_uid: section.humanized_uid,
            message: "Failed to sync LMS resources for section #{section.humanized_uid}",
          )
        end
      end
    end

    def cohorts
      @cohorts ||= Cohort.where(status: Cohort::LMS_OPENED_STATUSES)
        .joins(:remote_canvas_course)
    end

    def sections
      @sections ||= Section.where(cohort_id: cohorts.ids).joins(:remote_canvas_section)
    end
  end
end
