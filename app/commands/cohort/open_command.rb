# frozen_string_literal: true

class Cohort
  class OpenCommand < ApplicationCommand
    EMAIL_DELAY = 5.hours # Send activation emails at 7am PST. Modify Delay if <PERSON> runs at a different time.

    class << self
      def openable?(cohort:)
        new(cohort:, notify_in: EMAIL_DELAY).openable?
      end
    end

    attr_reader :cohort, :notify_in

    def initialize(cohort:, notify_in: EMAIL_DELAY)
      @cohort = cohort
      @notify_in = notify_in
    end

    def call!
      unless openable?
        ErrorReporter.report(
          error: StandardError.new('Cohort is not openable'),
          source: self.class.name,
          severity: :warning,
          cohort_id: cohort.id,
          message: "Attempted to open cohort #{cohort.id} but it is not openable",
        )
        return
      end

      log(action: 'open_cohort', cohort_id: cohort.id)

      update_cohort_opened
      activate_enrollments
    end

    def openable?
      cohort.created? &&
        cohort.lms_opens_on <= Time.current &&
        cohort_has_required_setup?
    end

    private

    def update_cohort_opened
      cohort.update!(status: :opened)
    end

    def activate_enrollments
      enrollments.pending_status.each do |enrollment|
        Enrollment::ActivateCommand.call!(enrollment:, notify_in:)
      rescue => e
        ErrorReporter.report(
          error: e,
          source: self.class.name,
          severity: :fatal,
          enrollment_uid: enrollment.humanized_uid,
          message: "Failed to activate enrollment: #{e.message}",
        )
      end
    end

    def enrollments
      @enrollments ||= cohort.enrollments.primary
    end

    def cohort_has_required_setup?
      return false if cohort.remote_canvas_course.blank?

      cohort_with_sections = Cohort.where(id: cohort.id)
        .joins(:sections, :remote_canvas_course)
        .merge(Section.joins(:remote_canvas_section))
        .where("sections.chat_join_url IS NOT NULL
              AND sections.chat_workspace_key IS NOT NULL
              AND sections.conferencing_url IS NOT NULL",
              )
        .group("cohorts.id")
        .having("COUNT(sections.id) = (SELECT COUNT(*) FROM sections WHERE sections.cohort_id = cohorts.id)")

      cohort_with_sections.exists?
    end

    def log(**args)
      JsonLogger.log(type: 'Cohort::OpenCommand', **args)
    end
  end
end
