import { Controller } from "@hotwired/stimulus";

// Connects to data-controller="copy-zoom-link"
export default class extends Controller {
  static values = { url: String }

  copy() {
    if (!this.hasUrlValue || !this.urlValue) {
      console.warn("No zoom URL provided to copy");
      return;
    }

    if (navigator.clipboard) {
      navigator.clipboard.writeText(this.urlValue)
        .then(() => {
          this.showFeedback("Copied zoom link!");
        })
        .catch(err => {
          console.error("Failed to copy zoom link: ", err);
          this.showFeedback("Failed to copy zoom link.");
        });
    } else {
      // Fallback for older browsers
      this.fallbackCopy();
    }
  }

  fallbackCopy() {
    const tempTextarea = document.createElement('textarea');
    tempTextarea.value = this.urlValue;
    document.body.appendChild(tempTextarea);
    tempTextarea.select();
    
    try {
      document.execCommand('copy');
      this.showFeedback("Copied zoom link!");
    } catch (err) {
      console.error("Fallback copy failed: ", err);
      this.showFeedback("Failed to copy zoom link.");
    }
    
    document.body.removeChild(tempTextarea);
  }

  showFeedback(message) {
    // Create a temporary tooltip/notification
    const tooltip = document.createElement('div');
    tooltip.textContent = message;
    tooltip.className = 'fixed top-4 right-4 bg-green-600 text-white px-4 py-2 rounded shadow-lg z-50 transition-opacity duration-300';
    
    document.body.appendChild(tooltip);
    
    // Remove the tooltip after 2 seconds
    setTimeout(() => {
      tooltip.style.opacity = '0';
      setTimeout(() => {
        if (tooltip.parentNode) {
          document.body.removeChild(tooltip);
        }
      }, 300);
    }, 2000);
  }
}
