# frozen_string_literal: true

# == Schema Information
#
# Table name: remote_resources
#
#  id               :bigint           not null, primary key
#  core_record_type :string           not null
#  fetched_at       :datetime
#  key              :string           default(""), not null
#  published_at     :datetime
#  type             :string           not null
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  core_record_id   :bigint           not null
#
# Indexes
#
#  index_remote_resources_on_core_record                   (core_record_type,core_record_id)
#  index_remote_resources_on_type_and_key_and_core_record  (type,key,core_record_type,core_record_id) UNIQUE
#
module Remote
  module Hubspot
    class Deal < Remote::Resource
      CRM_TIME_ZONE = ::Deal::CRM_TIME_ZONE

      include BaseActions

      has_remote_parent 'Remote::Hubspot::Contact'

      alias_method :deal, :core_record
      delegate :name, :stage, :partner, :ecom_order, :latest_ad_tracked_event, :closed_won_by_clas?, to: :deal
      delegate :partner_program, to: :deal
      delegate :remote_hubspot_product, to: :partner_program
      delegate :certificate_properties, to: :enrollment_presenter
      delegate :partner_branding_elements_properties, to: :remote_hubspot_product

      cattr_reader(:alternate_key_property) { 'core_deal_id' }
      def alternate_key
        env_id = case Rails.env
        when 'production'  then ''
        when 'staging'     then 'STG-'
        when 'development' then 'DEV-'
        else 'TEST-'
        end
        [env_id, deal.id].join
      end

      def remote_publishable?
        super && remote_hubspot_product.present?
      end

      def default_payload
        Time.use_zone(CRM_TIME_ZONE) do
          {
            "properties" => {
              **cloned_product_properties,
              **deal_properties,
              **section_properties,
              **ecom_order_properties,
              **enrollment_properties,
              **site_ad_tracking_properties,
              **registration_properties,
            },
            "associations" => [
              { to: { id: remote_parent.key }, types: [{ associationCategory: "HUBSPOT_DEFINED", associationTypeId: 3 }] },
            ],
          }.with_indifferent_access
        end
      end

      def course_risk_payload
        {
          "properties" => {
            "learner_risk_level" => enrollment.course_risk_name,
            "learner_course_risk_date_updated" => enrollment.course_risk_reviewed_at.in_time_zone('Pacific Time (US & Canada)').to_date,
          },
        }.with_indifferent_access
      end

      def risk_assessment_payload
        {
          "properties" => {
            "learner_activation_status" => enrollment_presenter.send(:learner_activation_status),
          },
        }.with_indifferent_access
      end

      def pass_status_payload
        {
          "properties" => stage_properties.merge(pass_status_properties),
        }.with_indifferent_access
      end

      def no_pass_payload
        {
          "properties" => {
            **stage_properties,
            internal_lifecycle: "Concluded Learner",
            learner_status: "No Pass",
          },
        }.with_indifferent_access
      end

      def stale_status_payload
        {
          "properties" => stage_properties,
        }.with_indifferent_access
      end

      def section_attributes_payload
        {
          "properties" => section_properties,
        }.with_indifferent_access
      end

      def certificate_payload
        { "properties" => stage_properties.merge(certificate_properties) }.with_indifferent_access
      end

      def partner_branding_elements_payload
        # if ecom order is present, override generic payment urls with order-specific ones
        {
          "properties" => partner_branding_elements_properties
            .merge(ecom_order_properties.slice(:partner_product_payment_url, :short_payment_url)),
        }.with_indifferent_access
      end

      private

      cattr_reader(:student_pipeline) { config.dig(:deal, :student_pipeline, :id) }

      PIPELINE_STAGE_KEY_LOOKUP = config # see config/remote_resources.yml
        .dig(:deal, :student_pipeline, :stages)
        .transform_values { |v| v[:stage_key] }
        .with_indifferent_access
      private_constant :PIPELINE_STAGE_KEY_LOOKUP

      HUBSPOT_DEAL_STATUS_KEY_LOOKUP = config # see config/remote_resources.yml
        .dig(:deal, :student_pipeline, :stages)
        .transform_values { |v| v[:status_key] }
        .with_indifferent_access
      private_constant :HUBSPOT_DEAL_STATUS_KEY_LOOKUP
      def deal_status_key
        HUBSPOT_DEAL_STATUS_KEY_LOOKUP[stage]
      end

      def pipeline_stage_key
        PIPELINE_STAGE_KEY_LOOKUP[stage]
      end

      def stage_properties
        {
          dealstage: pipeline_stage_key,
          deal_status: deal_status_key,
          internal_lifecycle: deal.stage_name,
        }
      end

      def enrollment
        deal.enrollments.primary.take
      end

      def enrollment_presenter
        @enrollment_presenter ||= EnrollmentPresenter.new(enrollment:)
      end

      def enrollment_properties
        return {} unless deal.status_closed_won? && enrollment

        enrollment_presenter.properties
      end

      def deal_properties
        Time.use_zone(CRM_TIME_ZONE) do
          {
            'dealname' => name,
            'pipeline' => student_pipeline,
            **stage_properties,
            'core_deal_id' => alternate_key,
            # milestone dates
            'closedate' => deal.close_date.utc.iso8601,
            'lead_date' => deal.became_a_lead_on,
            'became_a_registrant_date' => deal.first_became_a_registrant_on,
            'became_paid_enrollee' => deal.became_a_paid_enrollee_on,
            'last_engaged_at' => deal.last_engaged_at&.utc&.iso8601,
            'source' => deal.source_name,
            'is_primary_open_deal_for_contact' => deal.primary_open_deal_for_contact?,
            'probability_of_enrollment_after_7_days' => deal.probability_of_enrollment_after_7_days,
            'referrer_code' => latest_ad_tracked_event&.promos_referrer&.code,
          }.with_indifferent_access
        end
      end

      def pass_status_properties
        {
          learner_status: "Pass",
          learner_pass_date: enrollment_presenter.learner_pass_date,
        }.with_indifferent_access
      end

      REGISTRATION_ASPIRATION_HUBSPOT_VALUES = {
        other: 'Other',
        begin: 'Begin my professional career',
        advance: 'Advance in my current career',
        change: 'Change or re-enter my career',
      }.with_indifferent_access.freeze
      private_constant :REGISTRATION_ASPIRATION_HUBSPOT_VALUES

      def registration_properties
        return {} unless latest_registration

        {
          'do_you_have_prior_experience' => latest_registration.experience_level,
          'what_do_you_hope_to_do_upon_completing_this_program_' => REGISTRATION_ASPIRATION_HUBSPOT_VALUES[latest_registration.aspiration],
        }.with_indifferent_access
      end

      def latest_registration
        deal.registrations.latest
      end

      def ecom_order_properties
        return {} unless ecom_order

        pricing = ecom_order.pricing ||
          Ecom::Pricing::BuildFromOrderCommand.call!(order: ecom_order, payment_method: Ecom::PaymentMethod.find_by(kind: :upfront))
        partner_product_payment_url = UrlBuilder::Site.new(partner_program:).call!(template: :payment, order_id: ecom_order.humanized_uid)
        short_payment_url = ecom_order.short_payment_url.presence ||
          Ecom::Order::UpdateShortPaymentUrlCommand.call!(order: ecom_order).short_payment_url

        {
          amount: pricing.total_amount,
          partner_product_payment_url:,
          short_payment_url:,
        }
      end

      def section
        deal.highest_pre_learner_activity.try(:section)
      end

      def section_properties
        return {} unless section

        Time.use_zone(partner.time_zone) do
          starts_on, ends_on = section.cohort.values_at(:starts_on, :ends_on)
          start_finish = [starts_on, ends_on].map { |d| d.strftime('%B %-d') }.join(' - ')
          {
            'cohort_section' => section.name,
            'cohort_live_time' => Section::LiveDayTimeFormatter.new(section:).to_fs(format: :email),
            'cohort_live_day_of_week' => section.live_day_of_the_week_name,
            'cohort_lms_open_date' => section.cohort.lms_opens_on.to_s,
            'cohort_start_date' => starts_on.to_s,
            'cohort_end_date' => ends_on.to_s,
            'cohort_date_start_finish' => start_finish,
            'cohort_start_date_text' => starts_on.strftime('%-m/%-d'),
            'cohort_start_month_text' => starts_on.strftime('%B'),
            'cohort_slack_workspace' => section.chat_workspace_key,
            'cohort_slack_join_link' => section.chat_join_url,
            'cohort_zoom_link' => section.conferencing_url,
          }.with_indifferent_access
        end
      end

      def cloned_product_properties
        return {} unless remote_hubspot_product

        # clone the product properties to the deal to simplify consuming information in Hubspot Automations & Reporting
        product_properties = remote_hubspot_product.default_payload[:properties]

        product_properties.except(:name, :hs_sku, :hs_product_id, :hs_product_type, :price) # exclude these from the deal properties)
          .merge(amount: product_properties[:price])
          .merge(remote_hubspot_product.nomenclature_payload[:properties])
      end

      def site_ad_tracking_properties
        # To keep the list of attributes synced fixed, return hash with all keys and values nil if no ad tracking event is found
        site_ad_tracking = latest_ad_tracked_event&.site_ad_tracking || Site::AdTracking.new

        site_ad_tracking.slice(
          :fbclid,
          :gclid,
          :utm_campaign,
          :utm_content,
          :utm_medium,
          :utm_source,
          :utm_term,
          :hutk_cookie_id,
        )
      end
    end
  end
end
