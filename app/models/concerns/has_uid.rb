# frozen_string_literal: true

# Assumes the model including the concern has a uid column added to it.
# This also updates the record slug in urls to use the uid.
# Ex
# class Record
#   include HasUid
#
#   has_uid prefix: 'R'
# end
#
# Record.uid('R-123').first!
# Record.find_from_uid('R-123')
# Record.find_from_uid!('R-123')
# record.humanized_uid
#
module HasUid
  extend ActiveSupport::Concern

  DEFAULT_UID_LENGTH = 10

  @prefixes = {}

  class << self
    attr_reader :prefixes

    def register_prefix(prefix, source:)
      prefix = prefix.upcase

      if prefixes[prefix].present? && !(source < prefixes[prefix]) # rubocop:disable Style/InverseMethods
        raise ArgumentError.new("Uid prefix #{prefix} already registered to #{prefixes[prefix]}!")
      end

      prefixes[prefix] ||= source
    end

    def prefix_from_uid(uid)
      registered_prefixes = HasUid.prefixes.keys

      matching_prefixes = registered_prefixes.select do |prefix|
        uid.start_with?(prefix.to_s)
      end

      matching_prefixes.max_by(&:length) if matching_prefixes.any?
    end

    def model_for_prefix(prefix)
      prefixes[prefix&.upcase]
    end

    def find_record_by_uid(uid)
      prefix = prefix_from_uid(uid)
      model = model_for_prefix(prefix)
      model&.find_from_uid(uid)
    end
  end

  class_methods do
    attr_reader :uid_prefix

    def has_uid(prefix:, length: DEFAULT_UID_LENGTH)
      @uid_prefix = prefix
      @uid_length = length

      HasUid.register_prefix(prefix, source: self)

      generate_public_uid(column: :uid, generator: ReadableString.new(prefix: "#{@uid_prefix}-", length:))
      before_validation :generate_uid

      validates :uid, presence: true, uniqueness: true

      scope :uid, ->(uid) { where(uid: sanitize_uid(uid)) }
      scope :uid_cont, ->(uid) { where("#{table_name}.uid like ?", "#{uid_prefix}-%#{sanitized_body(uid)}%") } # override active admin filter
    end

    def sanitize_uid(uid)
      return nil if uid.nil?

      "#{uid_prefix}-#{sanitized_body(uid)}"
    end

    def sanitized_body(uid)
      uid_body(uid).gsub(/[^\w\d]/, '').upcase
    end

    # Takes a humanized uid and returns the original uid. Ex: "O-123-456-789" -> "O-123456789".
    def unhumanize_uid(humanized_uid)
      return if humanized_uid.nil?
      return humanized_uid unless humanized_uid.starts_with?(uid_prefix)

      humanized_uid.split('-').then do |prefix, *body|
        [prefix, body.join].join('-')
      end
    end

    def uid_body(uid)
      uid.upcase.delete_prefix("#{uid_prefix}-") || ''
    end

    def find_from_uid(uid)
      uid(uid).first
    end

    def find_from_uid!(uid)
      uid(uid).first!
    end

    def find_from_uid_or_id(id)
      find_from_uid(id.to_s) || find_by_id(id)
    end

    def find_from_uid_or_id!(id)
      find_from_uid(id.to_s) || find(id)
    end
  end


  def uid_prefix
    self.class.uid_prefix || ''
  end

  def uid_length
    self.class.instance_variable_get(:@uid_length)
  end

  def humanized_uid
    return nil if uid.nil?

    @humanized_uid ||= begin
      uid_body = self.class.uid_body(uid)
      "#{uid_prefix}-#{uid_body[0..2]}-#{uid_body[3..5]}-#{uid_body[6..]}"
    end
  end

  def to_param
    humanized_uid
  end

  def display_name
    humanized_uid
  end
end
