# frozen_string_literal: true

# == Schema Information
#
# Table name: learning_delivery_tasks
#
#  id             :bigint           not null, primary key
#  assigned_at    :datetime
#  completed_at   :datetime
#  description    :text
#  due_at         :datetime
#  reason         :string
#  recommendation :text
#  resource_type  :string
#  status         :integer          default("created"), not null
#  sub_type       :string
#  title          :string           not null
#  type           :string           not null
#  uid            :string           not null
#  viewed_at      :datetime
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  assigned_by_id :bigint
#  owner_id       :bigint
#  resource_id    :bigint
#  task_group_id  :bigint
#
# Indexes
#
#  index_learning_delivery_tasks_on_assigned_by_id  (assigned_by_id)
#  index_learning_delivery_tasks_on_owner_id        (owner_id)
#  index_learning_delivery_tasks_on_resource        (resource_type,resource_id)
#  index_learning_delivery_tasks_on_task_group_id   (task_group_id)
#  index_learning_delivery_tasks_on_uid             (uid) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (assigned_by_id => admin_users.id)
#  fk_rails_...  (owner_id => admin_users.id)
#  fk_rails_...  (task_group_id => learning_delivery_task_groups.id)
#
module LearningDelivery
  class Task
    class FixMergedRemoteContact < Task
      alias_method :remote_hubspot_contact, :resource

      class << self
        delegate :status_names, to: :superclass
      end

      has_uid prefix: 'LD-T'

      validates :resource_type, presence: true
      validate :resource_must_be_a_remote_hubspot_contact

      private

      def resource_must_be_a_remote_hubspot_contact
        return if resource_type.blank?

        return if resource.type == 'Remote::Hubspot::Contact'

        errors.add(:resource, 'must be a Remote::Hubspot::Contact')
      end
    end
  end
end
