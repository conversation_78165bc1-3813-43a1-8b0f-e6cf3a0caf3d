# frozen_string_literal: true

# == Schema Information
#
# Table name: learning_delivery_tasks
#
#  id             :bigint           not null, primary key
#  assigned_at    :datetime
#  completed_at   :datetime
#  description    :text
#  due_at         :datetime
#  reason         :string
#  recommendation :text
#  resource_type  :string
#  status         :integer          default("created"), not null
#  sub_type       :string
#  title          :string           not null
#  type           :string           not null
#  uid            :string           not null
#  viewed_at      :datetime
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  assigned_by_id :bigint
#  owner_id       :bigint
#  resource_id    :bigint
#  task_group_id  :bigint
#
# Indexes
#
#  index_learning_delivery_tasks_on_assigned_by_id  (assigned_by_id)
#  index_learning_delivery_tasks_on_owner_id        (owner_id)
#  index_learning_delivery_tasks_on_resource        (resource_type,resource_id)
#  index_learning_delivery_tasks_on_task_group_id   (task_group_id)
#  index_learning_delivery_tasks_on_uid             (uid) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (assigned_by_id => admin_users.id)
#  fk_rails_...  (owner_id => admin_users.id)
#  fk_rails_...  (task_group_id => learning_delivery_task_groups.id)
#
module LearningDelivery
  class Task
    class ReachOut < Task
      alias_method :risk_assessment, :resource

      class << self
        delegate :status_names, to: :superclass
      end

      has_uid prefix: 'LD-T'

      validates :sub_type, presence: true
      validates :resource_type, presence: true, inclusion: { in: %w[LearningDelivery::RiskAssessment] }

      delegate :enrollment, to: :risk_assessment
      delegate :learner, :section, to: :enrollment
    end
  end
end
