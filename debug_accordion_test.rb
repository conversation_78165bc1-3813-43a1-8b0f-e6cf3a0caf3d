require 'rails_helper'

describe 'Debug accordion rendering' do
  let(:instructor) { create(:learning_delivery_employee_instructor) }
  let(:cohort) { create(:cohort, :configured, status: :started) }
  let(:section) do
    create(:section,
      cohort:,
      instructor:,
      live_day_of_the_week: 1,
      live_start_time: '14:00:00',
      conferencing_url: 'https://zoom.us/j/123456789',
    )
  end
  let(:employee) { instructor }
  let(:presenter) { LearningDelivery::Employees::AccordionPresenter.new(employee) }

  it 'debug what gets rendered' do
    employee.sections << section
    
    puts "Section active?: #{section.active?}"
    puts "Cohort status: #{section.cohort.status}"
    puts "Presenter sections: #{presenter.sections.count}"
    puts "Active sections: #{presenter.sections.select(&:active?).count}"
    
    rendered = render_to_string(partial: 'learning_delivery/employees/accordion', locals: { presenter: })
    puts "Rendered HTML:"
    puts rendered
    
    expect(true).to be true
  end
end
