# frozen_string_literal: true

require 'rails_helper'

class Cohort
  describe SyncLmsResourcesJob do
    describe '#perform' do
      before do
        allow(Cohort::SyncLmsResourcesCommand).to receive(:call!)
      end

      it 'calls the Cohort::SyncLmsResourcesCommand' do
        described_class.new.perform

        expect(Cohort::SyncLmsResourcesCommand).to have_received(:call!)
      end
    end
  end
end
