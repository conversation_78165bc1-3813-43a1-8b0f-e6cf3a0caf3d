# frozen_string_literal: true

require 'rails_helper'

module LearningDelivery
  module Employees
    describe AccordionPresenter, type: :presenter do
      let(:instructor) { create(:learning_delivery_employee_instructor) }
      let(:advocate) { create(:learning_delivery_employee_advocate) }
      let(:cohort) { create(:cohort, :configured) }
      let(:section) do
        create(:section,
          cohort:,
          instructor:,
          advocate:,
          live_day_of_the_week: 1, # Monday
          live_start_time: '14:00:00',
          conferencing_url: 'https://zoom.us/j/123456789',
        )
      end
      let(:employee) { instructor }
      let(:presenter) { described_class.new(employee) }

      before do
        # Associate the employee with the section
        employee.sections << section
      end

      describe '#initialize' do
        it 'sets the sections from the employee' do
          expect(presenter.sections).to eq(employee.sections)
        end
      end

      describe '#live_day_and_time' do
        it 'returns formatted live day and time' do
          expect(presenter.live_day_and_time(section)).to be_a(String)
          expect(presenter.live_day_and_time(section)).to include('Mon')
        end
      end

      describe '#enrollments_at_high_risk_count' do
        it 'returns the count of high risk enrollments' do
          expect(presenter.enrollments_at_high_risk_count(section)).to eq(0)
        end
      end

      describe '#enrollments_count' do
        it 'returns the count of retained enrollments' do
          expect(presenter.enrollments_count(section)).to eq(0)
        end
      end

      describe '#enrollments_percentage' do
        context 'when there are no enrollments' do
          it 'returns 0' do
            expect(presenter.enrollments_percentage(section)).to eq(0)
          end
        end
      end

      describe '#format_section_title' do
        it 'returns formatted section title' do
          title = presenter.format_section_title(section)
          expect(title).to include(section.cohort.program.abbreviation)
          expect(title).to include('Section')
          expect(title).to include(section.suffix.to_s)
        end
      end

      describe '#week_number' do
        it 'returns the cohort week number' do
          expect(presenter.week_number(section)).to eq(section.cohort.week_number)
        end
      end

      describe '#conferencing_url' do
        it 'returns the section conferencing URL' do
          expect(presenter.conferencing_url(section)).to eq('https://zoom.us/j/123456789')
        end

        context 'when section has no conferencing URL' do
          let(:section) do
            create(:section,
              cohort:,
              instructor:,
              advocate:,
              live_day_of_the_week: 1,
              live_start_time: '14:00:00',
              conferencing_url: nil,
            )
          end

          it 'returns nil' do
            expect(presenter.conferencing_url(section)).to be_nil
          end
        end
      end

      describe '#to_partial_path' do
        it 'returns the correct partial path' do
          expect(presenter.to_partial_path).to eq('learning_delivery/employees/accordion')
        end
      end
    end
  end
end
