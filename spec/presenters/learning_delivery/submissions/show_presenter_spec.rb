# frozen_string_literal: true

require 'rails_helper'

module LearningDelivery
  module Submissions
    RSpec.describe ShowPresenter, type: :presenter do
      let(:grading_config) { create(:lms_grading_config) }
      let(:assignment_template) { create(:lms_assignment_template, grading_config:) }
      let(:existing_submissions) { 2.times.flat_map { create_list(:lms_submission, 10, assignment_template:) } }
      let(:submission) { create(:lms_submission, attempt: 1, assignment_template:) }
      let(:review) { create(:lms_submission_review, submission:) }
      let(:current_employee) { create(:learning_delivery_employee) }
      let(:params) { {} }
      let(:presenter) do
        described_class.new(
          submission_id: submission.id,
          params:,
          current_employee:,
        )
      end

      describe '#to_partial_path' do
        it 'returns the correct partial path' do
          expect(presenter.to_partial_path).to eq('learning_delivery/submissions/show')
        end
      end

      describe '#index_path' do
        let(:ids) { existing_submissions.pluck(:id) << submission.id }

        before do
          allow_any_instance_of(Lms::Submission::ForGraderQuery).to receive(:relation).and_return(Lms::Submission.where(id: ids))
        end

        context 'with per_page parameter' do
          let(:params) { { per_page: '5' } }

          it 'returns the correct index path with calculated page' do
            expected_page = (21 / 5) + 1
            expected_path = UrlBuilder::LearningDelivery.learning_delivery_submissions_path(
              params: params.merge(page: expected_page),
            )
            expect(presenter.index_path).to eq(expected_path)
          end
        end

        context 'without per_page parameter' do
          let(:params) { {} }

          it 'uses Pagy default limit' do
            expected_path = UrlBuilder::LearningDelivery.learning_delivery_submissions_path(
              params: params.merge(page: 2),
            )
            expect(presenter.index_path).to eq(expected_path)
          end
        end

        context 'when no submissions are present' do
          let(:ids) { [] }

          it 'returns the index path with page 1' do
            expected_path = UrlBuilder::LearningDelivery.learning_delivery_submissions_path(
              params: params.merge(page: 1),
            )
            expect(presenter.index_path).to eq(expected_path)
          end
        end
      end

      describe '#publish_path' do
        it 'returns the correct publish path' do
          expect(presenter.publish_path).to eq(
            UrlBuilder::LearningDelivery.publish_learning_delivery_submission_path(id: submission.id),
          )
        end
      end

      describe '#show_path_for' do
        it 'returns the correct show path for given submission_id' do
          expect(presenter.show_path_for(submission.id)).to eq(
            UrlBuilder::LearningDelivery.learning_delivery_submission_path(
              id: submission.id,
              params:,
            ),
          )
        end
      end

      describe '#review_update_path' do
        context 'when there is a current review' do
          before { review }

          it 'returns the correct review update path' do
            expect(presenter.review_update_path).to eq(
              UrlBuilder::LearningDelivery.learning_delivery_submission_submission_review_path(
                submission_id: submission.id,
                id: submission.current_review.id,
              ),
            )
          end
        end

        context 'when there is no current review' do
          it 'returns nil' do
            expect(presenter.review_update_path).to be_nil
          end
        end
      end

      describe '#student_name' do
        it 'returns the student name' do
          expect(presenter.student_name).to eq(submission.learner.full_name)
        end
      end

      describe '#assignment_name' do
        it 'returns the assignment name' do
          expect(presenter.assignment_name).to eq(submission.assignment.name)
        end
      end

      describe '#section_name' do
        it 'returns the section name' do
          expect(presenter.section_name).to eq(submission.section.name)
        end
      end

      describe '#week' do
        it 'returns the week number' do
          expect(presenter.week).to eq(submission.cohort_week&.number)
        end
      end

      describe '#status_presenter' do
        it 'returns a StatusPresenter instance for the current review' do
          review
          expect(presenter.status_presenter).to be_a(StatusPresenter)
          expect(presenter.status_presenter.object).to eq(submission.current_review)
        end
      end

      describe '#exercise_review_presenters' do
        context 'when there are exercise reviews' do
          let!(:exercise_review) { create(:lms_submission_exercise_review, review:) }

          it 'returns an array of ExerciseReviewPresenter instances' do
            expect(presenter.exercise_review_presenters).to all(be_a(ShowPresenter::ExerciseReviewPresenter))
            expect(presenter.exercise_review_presenters.first.exercise_review).to eq(exercise_review)
          end
        end

        context 'when there are no exercise reviews' do
          it 'returns an empty array' do
            expect(presenter.exercise_review_presenters).to be_empty
          end
        end

        context 'when there are multiple exercise reviews with different order values' do
          let!(:exercise_config_1) { create(:lms_exercise_config, grading_config:, order: 2, title: 'Exercise 1') }
          let!(:exercise_config_2) { create(:lms_exercise_config, grading_config:, order: 1, title: 'Exercise 2') }
          let!(:exercise_config_3) { create(:lms_exercise_config, grading_config:, order: 3, title: 'Exercise 3') }

          let!(:exercise_review_1) { create(:lms_submission_exercise_review, review:, title: exercise_config_1.title) }
          let!(:exercise_review_2) { create(:lms_submission_exercise_review, review:, title: exercise_config_2.title) }
          let!(:exercise_review_3) { create(:lms_submission_exercise_review, review:, title: exercise_config_3.title) }

          it 'orders exercise reviews by exercise config order and then by id' do
            expect(presenter.exercise_review_presenters.map(&:exercise_review)).to eq(
              [
                exercise_review_2, exercise_review_1, exercise_review_3,
              ],
            )
          end
        end
      end

      describe '#total_submissions' do
        before do
          mock_relation = instance_double(ActiveRecord::Relation)
          allow(mock_relation).to receive(:pluck).with(:id).and_return([1, 2, 3])
          allow_any_instance_of(Lms::Submission::ForGraderQuery).to receive(:relation).and_return(mock_relation)
        end

        it 'returns the total number of submissions from the query' do
          expect(presenter.total_submissions).to eq(3)
        end
      end

      describe '#current_position' do
        before do
          mock_relation = instance_double(ActiveRecord::Relation)
          allow(mock_relation).to receive(:pluck).with(:id).and_return([submission.id + 1, submission.id, submission.id + 2])
          allow_any_instance_of(Lms::Submission::ForGraderQuery).to receive(:relation).and_return(mock_relation)
        end

        it 'returns the position from pagy' do
          expect(presenter.current_position).to eq(2) # Second position
        end
      end

      describe '#submission_id_for_page' do
        before do
          mock_relation = instance_double(ActiveRecord::Relation)
          allow(mock_relation).to receive(:pluck).with(:id).and_return([submission.id + 1, submission.id, submission.id + 2, submission.id + 3])
          allow_any_instance_of(Lms::Submission::ForGraderQuery).to receive(:relation).and_return(mock_relation)
        end

        it 'returns the submission ID for a given page number' do
          expect(presenter.submission_id_for_page(1)).to eq(submission.id + 1)
          expect(presenter.submission_id_for_page(2)).to eq(submission.id)
          expect(presenter.submission_id_for_page(3)).to eq(submission.id + 2)
          expect(presenter.submission_id_for_page(4)).to eq(submission.id + 3)
        end

        it 'returns nil for invalid page numbers' do
          expect(presenter.submission_id_for_page(0)).to be_nil
          expect(presenter.submission_id_for_page(5)).to be_nil
        end
      end


      describe '#review_grade' do
        let(:review) { create(:lms_submission_review, :graded, submission:, grade: 'complete') }
        it 'returns the grade name of the current review' do
          review
          expect(presenter.review_grade).to eq('Complete')
        end

        it 'returns nil if there is no current review' do
          expect(presenter.review_grade).to be_nil
        end
      end

      describe '#review_grade_presenter' do
        let(:review) { create(:lms_submission_review, :graded, submission:, grade: 'complete') }
        it 'returns a GradePresenter instance' do
          expect(presenter.review_grade_presenter).to be_a(GradePresenter)
        end
      end

      describe '#grade_options_with_classnames' do
        let(:grade_presenter) { instance_double(GradePresenter, pill_css_class: 'grade-option') }
        before do
          allow(presenter).to receive(:review_grade_presenter).and_return(grade_presenter)
        end

        it 'returns an array of grade options with classnames' do
          options = presenter.grade_options_with_classnames
          expect(options).to be_an(Array)
          expect(options.first).to eq(['Incomplete', 0, 'grade-option'])
        end
      end

      describe '#review_comment' do
        let(:comment) { 'Test comment' }
        let(:review) { create(:lms_submission_review, submission:, comment:) }
        it 'returns the comment of the current review' do
          review
          expect(presenter.review_comment).to eq(comment)
        end

        it 'returns nil if there is no current review' do
          expect(presenter.review_comment).to be_nil
        end
      end

      describe '#review_id' do
        it 'returns the id of the current review' do
          review
          expect(presenter.review_id).to eq(review.id)
        end

        it 'returns nil if there is no current review' do
          expect(presenter.review_id).to be_nil
        end
      end
    end
  end
end
