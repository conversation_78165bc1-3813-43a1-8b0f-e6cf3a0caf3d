# frozen_string_literal: true

require 'rails_helper'

module LearningDelivery
  class Task
    describe RemoveLearnerPresenter do
      let!(:task_template) do
        create(:learning_delivery_task_template,
          task_type: :remove_learner,
          sub_type: nil,
          title: 'Remove {LEARNER_NAME}',
          description: 'Learner email: {LEARNER_EMAIL}, Section: {SECTION_INFO}, Profile: {PROFILE_LINK}',
        )
      end

      let!(:enrollment) { create(:enrollment) }
      let(:learner) { enrollment.learner }
      let(:section) { enrollment.section }
      let(:cohort) { section.cohort }
      let(:program) { cohort.program }

      let!(:remote_canvas_user) do
        create(:remote_canvas_user, core_record: learner)
      end

      let(:presenter) { described_class.new(resource: enrollment) }

      describe '#initialize' do
        it 'sets the resource' do
          expect(presenter.resource).to eq(enrollment)
        end

        context 'when resource is not an Enrollment' do
          let(:invalid_resource) { create(:learner) }

          it 'raises an ArgumentError' do
            expect do
              described_class.new(resource: invalid_resource)
            end.to raise_error(ArgumentError, /Expected Enrollment, got/)
          end
        end
      end

      describe '#task_template' do
        it 'finds the template by type and subtype' do
          expect(presenter.task_template).to eq(task_template)
        end
      end

      describe 'placeholder interpolation' do
        it 'interpolates the title with learner name' do
          expect(presenter.title).to eq("Remove #{learner.full_name}")
        end

        it 'interpolates the description with all placeholders' do
          expected_link = <<~PROFILE_LINK
 <a href="#{learner.remote_canvas_user.remote_link}" target="_blank"><span>Link to Profile</span></a>
          PROFILE_LINK

          expected = "Learner email: #{learner.primary_email_address}, " \
                     "Section: #{section.cohort.program.name} / " \
                     "#{section.cohort.name} / #{section.name} / " \
                     "Week #{section.cohort.week_number}, " \
                     "Profile: #{expected_link}"
          expect(presenter.description).to eq(expected)
        end
      end

      describe 'private methods' do
        describe '#placeholder_values' do
          it 'returns a hash with all required placeholders' do
            expect(presenter.send(:placeholder_values)).to eq(
              {
                '{LEARNER_NAME}' => :learner_name,
                '{LEARNER_EMAIL}' => :learner_email,
                '{SECTION_INFO}' => :section_info,
                '{PROFILE_LINK}' => :profile_link,
              },
            )
          end
        end

        describe '#learner_name' do
          it 'returns the full name of the learner' do
            expect(presenter.send(:learner_name)).to eq(learner.full_name)
          end
        end

        describe '#learner_email' do
          it 'returns the primary email address of the learner' do
            expect(presenter.send(:learner_email)).to eq(learner.primary_email_address)
          end
        end

        describe '#profile_link' do
          it 'returns an HTML anchor fragment with the remote link' do
            expected_link = <<~PROFILE_LINK
 <a href="#{learner.remote_canvas_user.remote_link}" target="_blank"><span>Link to Profile</span></a>
            PROFILE_LINK
            expect(presenter.send(:profile_link)).to eq(expected_link)
          end
        end

        describe '#section_info' do
          it 'returns formatted section information' do
            expected = "#{program.name} / #{cohort.name} / #{section.name} / Week #{cohort.week_number}"
            expect(presenter.send(:section_info)).to eq(expected)
          end
        end
      end
    end
  end
end
