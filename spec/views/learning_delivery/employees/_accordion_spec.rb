# frozen_string_literal: true

require 'rails_helper'

describe 'learning_delivery/employees/_accordion.html.slim', type: :view do
  let(:instructor) { create(:learning_delivery_employee_instructor) }
  let(:advocate) { create(:learning_delivery_employee_advocate) }
  let(:cohort) { create(:cohort, :configured, status: :started) }
  let(:section) do
    create(:section,
      cohort:,
      instructor:,
      advocate:,
      live_day_of_the_week: 1, # Monday
      live_start_time: '14:00:00',
      conferencing_url: 'https://zoom.us/j/123456789',
    )
  end
  let(:employee) { instructor }
  let(:presenter) { LearningDelivery::Employees::AccordionPresenter.new(employee) }

  before do
    # Associate the employee with the section
    employee.sections << section
    assign(:presenter, presenter)
  end

  it 'renders the accordion with active sections' do
    render partial: 'learning_delivery/employees/accordion', locals: { presenter: }

    expect(rendered).to include('Active Sections')
    expect(rendered).to include(presenter.format_section_title(section))
    expect(rendered).to include('Started')
  end

  it 'includes the zoom icon with copy functionality' do
    render partial: 'learning_delivery/employees/accordion', locals: { presenter: }

    expect(rendered).to include('data-controller="copy-to-clipboard"')
    expect(rendered).to include('data-action="click->copy-to-clipboard#copy"')
    expect(rendered).to include('cursor-pointer')
    expect(rendered).to include('relative group')
  end

  it 'includes the Tailwind tooltip' do
    render partial: 'learning_delivery/employees/accordion', locals: { presenter: }

    expect(rendered).to include('Copy zoom link')
    expect(rendered).to include('group-hover:opacity-100')
    expect(rendered).to include('transition-opacity')
  end

  it 'includes the hidden conferencing URL for copying' do
    render partial: 'learning_delivery/employees/accordion', locals: { presenter: }

    expect(rendered).to include('data-copy-to-clipboard-target="content"')
    expect(rendered).to include('https://zoom.us/j/123456789')
    expect(rendered).to include('hidden')
  end

  it 'includes the live session information' do
    render partial: 'learning_delivery/employees/accordion', locals: { presenter: }

    expect(rendered).to include('Live at')
    expect(rendered).to include(presenter.live_day_and_time(section))
  end

  context 'when section has no conferencing URL' do
    let(:section) do
      create(:section,
        cohort:,
        instructor:,
        advocate:,
        live_day_of_the_week: 1,
        live_start_time: '14:00:00',
        conferencing_url: nil,
      )
    end

    it 'still renders without errors' do
      expect { render partial: 'learning_delivery/employees/accordion', locals: { presenter: } }.not_to raise_error
    end

    it 'includes empty content for copy functionality' do
      render partial: 'learning_delivery/employees/accordion', locals: { presenter: }

      expect(rendered).to include('data-copy-to-clipboard-target="content"')
      # The content should be empty when conferencing_url is nil
      expect(rendered).to match(/data-copy-to-clipboard-target="content"[^>]*>\s*<\/span>/)
    end
  end

  context 'when employee has no active sections' do
    let(:cohort) { create(:cohort, :configured, status: :created) }

    it 'renders the accordion structure but no section content' do
      render partial: 'learning_delivery/employees/accordion', locals: { presenter: }

      expect(rendered).to include('Active Sections')
      expect(rendered).not_to include(presenter.format_section_title(section))
    end
  end
end
