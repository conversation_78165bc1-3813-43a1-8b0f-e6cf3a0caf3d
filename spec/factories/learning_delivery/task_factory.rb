# frozen_string_literal: true

# == Schema Information
#
# Table name: learning_delivery_tasks
#
#  id             :bigint           not null, primary key
#  assigned_at    :datetime
#  completed_at   :datetime
#  description    :text
#  due_at         :datetime
#  reason         :string
#  recommendation :text
#  resource_type  :string
#  status         :integer          default("created"), not null
#  sub_type       :string
#  title          :string           not null
#  type           :string           not null
#  uid            :string           not null
#  viewed_at      :datetime
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  assigned_by_id :bigint
#  owner_id       :bigint
#  resource_id    :bigint
#  task_group_id  :bigint
#
# Indexes
#
#  index_learning_delivery_tasks_on_assigned_by_id  (assigned_by_id)
#  index_learning_delivery_tasks_on_owner_id        (owner_id)
#  index_learning_delivery_tasks_on_resource        (resource_type,resource_id)
#  index_learning_delivery_tasks_on_task_group_id   (task_group_id)
#  index_learning_delivery_tasks_on_uid             (uid) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (assigned_by_id => admin_users.id)
#  fk_rails_...  (owner_id => admin_users.id)
#  fk_rails_...  (task_group_id => learning_delivery_task_groups.id)
#
FactoryBot.define do
  factory :learning_delivery_task, class: 'LearningDelivery::Task' do
    initialize_with { type.constantize.new }
    type { 'LearningDelivery::Task::Announcement' }

    parent_with_nested_args :assigned_by, factory: :admin_user, optional: true

    status { :created }
    title { 'Learning Task' }
    description { 'Task description goes here' }
    due_at { 1.week.from_now }

    trait :reach_out do
      type { 'LearningDelivery::Task::ReachOut' }
      sub_type { 'falling_behind' }
      parent_with_nested_args :resource, factory: :learning_delivery_risk_assessment
    end

    trait :announcement do
      type { 'LearningDelivery::Task::Announcement' }
      parent_with_nested_args :resource, factory: :section, optional: true
    end

    trait :remove_learner do
      type { 'LearningDelivery::Task::RemoveLearner' }
      parent_with_nested_args :resource, factory: :enrollment
    end

    trait :merged_remote_contact do
      type { 'LearningDelivery::Task::FixMergedRemoteContact' }
      parent_with_nested_args :resource, factory: :remote_hubspot_contact
    end

    trait :created do
      status { :created }
    end

    trait :assigned do
      assigned_at { Time.zone.now }
      with_assigned_by
      status { :assigned }
    end

    trait :viewed do
      assigned
      viewed_at { Time.zone.now }
      status { :viewed }
    end

    trait :in_progress do
      viewed
    end

    trait :completed do
      viewed
      completed_at { Time.zone.now }
      status { :completed }
    end

    trait :skipped do
      viewed
      status { :skipped }
    end

    trait :expired do
      assigned
      status { :expired }
      due_at { 1.week.ago }
    end

    trait :falling_behind do
      reach_out
      sub_type { 'falling_behind' }
    end

    trait :no_recent_activity do
      reach_out
      sub_type { 'no_recent_activity' }
      resource { create(:learning_delivery_risk_assessment, :no_recent_activity) }
    end

    trait :not_activated do
      reach_out
      sub_type { 'not_activated' }
      resource { create(:learning_delivery_risk_assessment, :not_activated) }
    end

    trait :with_recommendation do
      recommendation { 'This is a recommendation for the task' }
    end
  end
end
