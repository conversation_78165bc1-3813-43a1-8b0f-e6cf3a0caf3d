# frozen_string_literal: true

# == Schema Information
#
# Table name: external_content_codes
#
#  id              :bigint           not null, primary key
#  available_from  :datetime
#  available_until :datetime
#  code            :string           not null
#  max_uses        :integer          not null
#  uid             :string           not null
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  code_upload_id  :bigint
#  material_id     :bigint           not null
#
# Indexes
#
#  index_ecc_on_material_id                              (material_id)
#  index_external_content_codes_on_code_and_material_id  (code,material_id)
#  index_external_content_codes_on_code_upload_id        (code_upload_id)
#  index_external_content_codes_on_uid                   (uid) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (code_upload_id => external_content_code_uploads.id)
#  fk_rails_...  (material_id => external_content_materials.id)
#
require 'rails_helper'

module ExternalContent
  describe Code do
    let(:material) { create(:external_content_material) }
    it_behaves_like :a_factorified_class

    describe 'validations' do
      describe 'availability period validations' do
        subject(:code) { build(:external_content_code) }

        context 'when available_from is not set' do
          before { code.available_from = nil }

          it 'allows available_until to be nil' do
            code.available_until = nil
            expect(code).to be_valid
          end

          it 'allows available_until to be set' do
            code.available_until = 1.day.from_now
            expect(code).to be_valid
          end
        end

        context 'when available_from is set' do
          before { code.available_from = Time.current }

          it 'requires available_until to be set' do
            code.available_until = nil
            expect(code).not_to be_valid
            expect(code.errors[:available_until]).to include('must be present if available_from is set')
          end

          it 'requires available_until to be after available_from' do
            code.available_until = 1.day.ago
            expect(code).not_to be_valid
            expect(code.errors[:available_until]).to include('must be equal to or after available_from')
          end

          it 'allows available_until to be equal to available_from' do
            code.available_until = code.available_from
            expect(code).to be_valid
          end

          it 'allows available_until to be after available_from' do
            code.available_until = 1.day.from_now
            expect(code).to be_valid
          end
        end
      end

      describe 'code uniqueness' do
        let(:other_material) { create(:external_content_material) }
        let!(:existing_code) { create(:external_content_code, material:, code: 'TEST123') }

        context 'when code is available' do
          before do
            existing_code.update!(available_from: nil, available_until: nil) # Always available
          end

          it 'prevents duplicate codes for the same material' do
            new_code = build(:external_content_code, material:, code: 'TEST123')
            expect(new_code).not_to be_valid
            expect(new_code.errors[:code]).to include('has already been taken')
          end

          it 'allows same code for different materials' do
            new_code = build(:external_content_code, material: other_material, code: 'TEST123')
            expect(new_code).to be_valid
          end
        end

        context 'when code is not available' do
          before do
            existing_code.update!(
              available_from: 1.day.ago,
              available_until: 1.hour.ago,
            )
          end

          it 'allows duplicate codes for the same material' do
            new_code = build(:external_content_code, material:, code: 'TEST123')
            expect(new_code).to be_valid
          end
        end
      end
    end

    describe '.available (scope)' do
      let(:base_time) { Time.zone.local(2024, 1, 1, 12, 0) }
      let!(:no_window_code) { create(:external_content_code, material:, available_from: nil, available_until: nil) }
      let!(:current_code) do
        create(:external_content_code,
          material:,
          available_from: base_time - 1.day,
          available_until: base_time + 1.day,
        )
      end
      let!(:future_code) do
        create(:external_content_code,
          material:,
          available_from: base_time + 1.day,
          available_until: base_time + 2.days,
        )
      end
      let!(:past_code) do
        create(:external_content_code,
          material:,
          available_from: base_time - 2.days,
          available_until: base_time - 1.day,
        )
      end

      context 'when no time is provided' do
        around do |example|
          Timecop.freeze(base_time) { example.run }
        end

        it 'includes codes with no availability window' do
          expect(described_class.available).to include(no_window_code)
        end

        it 'includes codes where current time is within availability window' do
          expect(described_class.available).to include(current_code)
        end

        it 'excludes codes where current time is before availability window' do
          expect(described_class.available).not_to include(future_code)
        end

        it 'excludes codes where current time is after availability window' do
          expect(described_class.available).not_to include(past_code)
        end
      end

      context 'when time is provided' do
        it 'includes codes with no availability window' do
          expect(described_class.available(base_time + 2.days)).to include(no_window_code)
        end

        it 'includes codes where provided time is within availability window' do
          expect(described_class.available(base_time)).to include(current_code)
        end

        it 'includes codes where provided time makes them available' do
          expect(described_class.available(base_time + 1.5.days)).to include(future_code)
          expect(described_class.available(base_time - 1.5.days)).to include(past_code)
        end

        it 'excludes codes where provided time is outside availability window' do
          expect(described_class.available(base_time + 3.days)).not_to include(future_code)
          expect(described_class.available(base_time - 3.days)).not_to include(past_code)
        end
      end
    end

    describe '.expired (scope)' do
      let(:base_time) { Time.zone.local(2024, 1, 1, 12, 0) }
      let!(:no_window_code) { create(:external_content_code, material:, available_from: nil, available_until: nil) }
      let!(:current_code) do
        create(:external_content_code,
          material:,
          available_from: base_time - 1.day,
          available_until: base_time + 1.day,
        )
      end
      let!(:future_code) do
        create(:external_content_code,
          material:,
          available_from: base_time + 1.day,
          available_until: base_time + 2.days,
        )
      end
      let!(:past_code) do
        create(:external_content_code,
          material:,
          available_from: base_time - 2.days,
          available_until: base_time - 1.day,
        )
      end

      around do |example|
        Timecop.freeze(base_time) { example.run }
      end

      it 'excludes codes with no availability window' do
        expect(described_class.expired).not_to include(no_window_code)
      end

      it 'excludes codes with future available_until' do
        expect(described_class.expired).not_to include(current_code)
        expect(described_class.expired).not_to include(future_code)
      end

      it 'includes codes with past available_until' do
        expect(described_class.expired).to include(past_code)
      end
    end

    describe '#amount_used' do
      subject(:code) { create(:external_content_code) }

      context 'when there are no code uses' do
        it 'returns 0' do
          expect(code.amount_used).to eq(0)
        end
      end

      context 'when there are code uses with different statuses' do
        before do
          create(:external_content_code_use, :created, code:)
          create(:external_content_code_use, :delivered, code:)
          create(:external_content_code_use, :used, code:)
          create(:external_content_code_use, :abandoned, code:)
        end

        it 'returns the count of delivered and used code uses' do
          expect(code.amount_used).to eq(2)
        end
      end
    end

    describe '#amount_usable' do
      subject(:code) { create(:external_content_code, material:, max_uses: 5) }

      context 'when there are no code uses' do
        it 'returns max_uses' do
          expect(code.amount_usable).to eq(5)
        end
      end

      context 'when there are some code uses' do
        before do
          create(:external_content_code_use, :delivered, code:)
          create(:external_content_code_use, :used, code:)
          create(:external_content_code_use, :created, code:) # Not counted
          create(:external_content_code_use, :abandoned, code:) # Not counted
        end

        it 'returns max_uses minus amount_used' do
          # max_uses: 5, amount_used: 2 (delivered + used)
          expect(code.amount_usable).to eq(3)
        end
      end

      context 'when all uses are used up' do
        before do
          create(:external_content_code_use, :delivered, code:)
          create(:external_content_code_use, :delivered, code:)
          create(:external_content_code_use, :used, code:)
          create(:external_content_code_use, :used, code:)
          create(:external_content_code_use, :used, code:)
          create(:external_content_code_use, :created, code:) # Not counted
        end

        it 'returns 0' do
          # max_uses: 5, amount_used: 5 (2 delivered + 3 used)
          expect(code.amount_usable).to eq(0)
        end
      end
    end

    describe '#available?' do
      let(:base_time) { Time.zone.local(2024, 1, 1, 12, 0) }

      around do |example|
        Timecop.freeze(base_time) { example.run }
      end

      context 'when available_until is nil' do
        subject(:code) { build(:external_content_code, available_from: nil, available_until: nil) }

        it 'returns true' do
          expect(code).to be_available
        end
      end

      context 'when current time is within availability window' do
        subject(:code) do
          build(:external_content_code,
            available_from: base_time - 1.day,
            available_until: base_time + 1.day,
          )
        end

        it 'returns true' do
          expect(code).to be_available
        end
      end

      context 'when current time is before availability window' do
        subject(:code) do
          build(:external_content_code,
            available_from: base_time + 1.day,
            available_until: base_time + 2.days,
          )
        end

        it 'returns false' do
          expect(code).not_to be_available
        end
      end

      context 'when current time is after availability window' do
        subject(:code) do
          build(:external_content_code,
            available_from: base_time - 2.days,
            available_until: base_time - 1.day,
          )
        end

        it 'returns false' do
          expect(code).not_to be_available
        end
      end
    end

    describe '.usable' do
      let(:time) { Time.zone.now }

      context 'when code is available and under max uses' do
        let(:code) { create(:external_content_code, :with_available_period, material:, max_uses: 2) }
        let!(:code_use) { create(:external_content_code_use, :used, code:) }

        it 'includes the code' do
          expect(described_class.usable).to include(code)
        end
      end

      context 'when code is available but at max uses' do
        let(:code) { create(:external_content_code, :with_available_period, material:, max_uses: 1) }
        let!(:code_use) { create(:external_content_code_use, :used, code:) }

        it 'excludes the code' do
          expect(described_class.usable).not_to include(code)
        end
      end

      context 'when code is available and used, but use is marked abandoned' do
        let(:code) { create(:external_content_code, :with_available_period, material:, max_uses: 1) }
        let!(:code_use) { create(:external_content_code_use, :abandoned, code:) }

        it 'includes the code' do
          expect(described_class.usable).to include(code)
        end
      end

      context 'when code is expired' do
        let(:code) { create(:external_content_code, :expired, material:, max_uses: 2) }

        it 'excludes the code' do
          expect(described_class.usable).not_to include(code)
        end
      end

      context 'when code has no availability period' do
        let(:code) { create(:external_content_code, material:, max_uses: 2) }

        it 'includes the code' do
          expect(described_class.usable).to include(code)
        end
      end

      context 'with multiple codes in different states' do
        let!(:usable_code) { create(:external_content_code, :with_available_period, material:, max_uses: 2) }
        let!(:expired_code) { create(:external_content_code, :expired, material:, max_uses: 2) }
        let!(:maxed_code) do
          code = create(:external_content_code, :with_available_period, material:, max_uses: 1)
          create(:external_content_code_use, :used, code:)
          code
        end

        it 'only includes usable codes' do
          usable_codes = described_class.usable
          expect(usable_codes).to include(usable_code)
          expect(usable_codes).not_to include(expired_code)
          expect(usable_codes).not_to include(maxed_code)
        end
      end

      context 'with specific time parameter' do
        let(:future_time) { 2.days.from_now }
        let(:code) do
          create(:external_content_code, :with_available_period, material:,
            available_from: future_time - 1.day,
            available_until: future_time + 1.day,
            max_uses: 2,
          )
        end

        it 'respects the provided time' do
          expect(described_class.usable(future_time)).to include(code)
          expect(described_class.usable).not_to include(code)
        end
      end
    end

    describe '#usable?' do
      let(:code) { create(:external_content_code, :with_available_period, material:, max_uses: 2) }

      context 'when code is available and under max uses' do
        it 'returns true' do
          expect(code).to be_usable
        end
      end

      context 'when code is available but at max uses' do
        before do
          create_list(:external_content_code_use, 2, :used, code:)
        end

        it 'returns false' do
          expect(code).not_to be_usable
        end
      end

      context 'when code is expired' do
        let(:code) { create(:external_content_code, :expired, material:, max_uses: 2) }

        it 'returns false' do
          expect(code).not_to be_usable
        end
      end

      context 'when code has no availability period' do
        let(:code) { create(:external_content_code, material:, max_uses: 2) }

        it 'returns true' do
          expect(code).to be_usable
        end
      end

      context 'with non-used code_uses' do
        before do
          create(:external_content_code_use, :created, code:)
          create(:external_content_code_use, :delivered, code:)
        end

        it 'returns true' do
          expect(code).to be_usable
        end
      end

      context 'when code exists but has been used up' do
        let(:code) { create(:external_content_code, :with_available_period, material:, max_uses: 3) }

        before do
          create(:external_content_code_use, :used, code:)
          create(:external_content_code_use, :delivered, code:)
          create(:external_content_code_use, :created, code:) # Should not count
          create(:external_content_code_use, :abandoned, code:) # Should not count
          create(:external_content_code_use, :used, code:) # This makes 3 assumed_used
        end

        it 'returns false even though it is still available' do
          expect(code).to be_available
          expect(code.amount_used).to eq(3)
          expect(code).not_to be_usable
        end
      end
    end
  end
end
