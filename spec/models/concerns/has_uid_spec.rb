# frozen_string_literal: true

require 'rails_helper'

describe HasUid do
  let(:model_name) { 'uid_temp' }
  let(:model_class) { model_name.classify.constantize }
  let(:model_class_invokes_has_uid) { true }
  let(:prefix) { 'TEMP' }

  def build_model(attrs = {})
    model_class.new(attrs)
  end

  def create_model!(attrs = {})
    build_model(attrs).tap(&:save!)
  end

  before do
    # always start with no prefixes registered for uid specs
    allow(described_class).to receive(:prefixes).and_return({})
  end

  before do
    Temping.create(model_name.pluralize) do
      with_columns do |t|
        t.string :uid
      end
    end

    model_class.include(described_class)
    model_class.has_uid(prefix:) if model_class_invokes_has_uid
  end

  describe '.register_prefix' do
    let(:source) { model_class }
    let(:prefix_arg) { prefix }

    subject(:register_prefix) { described_class.register_prefix(prefix_arg, source:) }

    context 'with same prefix already registered' do
      it 'raises ArgumentError' do
        expect { register_prefix }.to raise_error(ArgumentError, 'Uid prefix TEMP already registered to UidTemp!')
      end

      context 'with subclass of registered class registered' do
        let(:source) { Class.new(model_class) }

        context 'with same prefix' do
          it 'registers only the superclass' do
            register_prefix

            expect(described_class.prefixes).to eq('TEMP' => model_class)
          end
        end

        context 'with different prefix' do
          let(:prefix_arg) { 'DIFF' }

          it 'registers the subclass with new prefix' do
            register_prefix

            expect(described_class.prefixes).to eq('TEMP' => model_class, 'DIFF' => source)
          end
        end
      end
    end
  end

  describe '.prefix_from_uid' do
    before do
      # Set up multiple prefixes for testing
      allow(described_class).to receive(:prefixes).and_return({
        'A' => Class.new,
        'AB' => Class.new,
        'ABC' => Class.new,
        'XYZ' => Class.new,
      },
                                                             )
    end

    it 'returns the matching prefix for a uid' do
      expect(described_class.prefix_from_uid('ABC-12345')).to eq('ABC')
    end

    it 'returns the longest matching prefix when multiple prefixes match' do
      expect(described_class.prefix_from_uid('AB-12345')).to eq('AB')
    end

    it 'returns nil when no prefix matches' do
      expect(described_class.prefix_from_uid('UNKNOWN-12345')).to be_nil
    end

    it 'is case sensitive' do
      # The current implementation is case sensitive
      expect(described_class.prefix_from_uid('abc-12345')).to be_nil
    end
  end

  describe '.model_for_prefix' do
    it 'returns the model for the given prefix' do
      expect(described_class.model_for_prefix(prefix)).to eq(model_class)
    end

    it 'returns nil for an unregistered prefix' do
      expect(described_class.model_for_prefix('UNREG')).to be_nil
    end
  end

  describe '.find_record_by_uid' do
    let!(:model) { create_model! }

    it 'returns the record for the given uid' do
      expect(described_class.find_record_by_uid(model.humanized_uid)).to eq(model)
    end

    it 'returns nil for an unregistered prefix' do
      expect(described_class.find_record_by_uid('UNREG-123')).to be_nil
    end

    it 'returns nil for a non-existent uid' do
      expect(described_class.find_record_by_uid("#{prefix}-NONEXISTENT")).to be_nil
    end
  end

  describe 'generate_uid (validation)' do
    let(:model) { build_model.tap(&:valid?) }
    let(:uid) { model.uid }

    subject(:valid?) { model.valid? }

    it 'generates a unique uid on validation with the prefix' do
      expect(valid?).to be(true)

      expect(model.uid).to match(/#{model.uid_prefix}-[\w\d]{#{model.uid_length}}/)
    end

    context 'when uid already exists' do
      let!(:existing_model) { create_model!(uid:) }

      it 'is not valid' do
        expect(valid?).to be(false)
      end
    end
  end

  describe '#humanized_uid, #to_param' do
    let(:model) { build_model.tap(&:valid?) }

    it 'is the uid with - separators' do
      expect(model.humanized_uid).to match(/#{model.uid_prefix}-[\w\d]{3}-[\w\d]{3}-[\w\d]{4}/)
      expect(model.to_param).to eq(model.humanized_uid)
    end

    context 'when uid is nil' do
      it 'is nil' do
        model.uid = nil

        expect(model.humanized_uid).to be_nil
        expect(model.to_param).to be_nil
      end
    end
  end

  describe '.uid_body' do
    it 'is the uid passed in without the prefix' do
      expect(model_class.uid_body("#{prefix}-123")).to eq('123')
    end

    context 'with prefix wrong case' do
      it 'is the uid passed in without the prefix' do
        expect(model_class.uid_body("#{prefix.downcase}-123")).to eq('123')
      end
    end
  end

  describe '.uid (scope)' do
    let!(:model) { create_model! }
    let!(:other_model) { create_model! }

    it 'finds models with the matching uid, ignoring all non-alphanumeric characters' do
      results = model_class.uid(model.humanized_uid)

      expect(results).to include(model)
      expect(results).not_to include(other_model)
    end

    context 'given nil' do
      it 'returns empty' do
        results = model_class.uid(nil)

        expect(results).to eq([])
      end
    end
  end

  describe '.uid_cont (scope)' do
    let!(:model) { create_model! }
    let!(:other_model) { create_model! }

    it 'finds models with the matching uid, ignoring all non-alphanumeric characters' do
      results = model_class.uid_cont(model.humanized_uid)

      expect(results).to include(model)
      expect(results).not_to include(other_model)
    end

    it 'finds models with partial matches' do
      prefix_results = model_class.uid_cont(model.humanized_uid.first(5))
      expect(prefix_results).to include(model)

      suffix_results = model_class.uid_cont(model.humanized_uid.last(5))
      expect(suffix_results).to include(model)
    end

    context 'given nil' do
      it 'returns empty' do
        results = model_class.uid(nil)

        expect(results).to eq([])
      end
    end
  end


  describe '.find_from_uid' do
    let!(:model) { create_model! }
    let!(:other_model) { create_model! }

    it 'find the model with the matching uid, ignoring all non-alphanumeric characters' do
      result = model_class.find_from_uid(model.humanized_uid)

      expect(result).to eq(model)
    end

    context 'with no matching model' do
      it 'returns nil' do
        result = model_class.find_from_uid('NOT-A-uid')

        expect(result).to be_nil
      end
    end
  end

  describe '.find_from_uid!' do
    let!(:model) { create_model! }
    let!(:other_model) { create_model! }

    it 'find the model with the matching uid, ignoring all non-alphanumeric characters' do
      result = model_class.find_from_uid!(model.humanized_uid)

      expect(result).to eq(model)
    end

    context 'with no matching model' do
      it 'raises' do
        expect { model_class.find_from_uid!('NOT-A-uid') }.to raise_error(ActiveRecord::RecordNotFound)
      end
    end
  end

  describe '.find_from_uid_or_id' do
    let!(:model) { create_model! }
    let!(:other_model) { create_model! }

    it 'find the model with the matching uid, ignoring all non-alphanumeric characters' do
      result = model_class.find_from_uid_or_id(model.humanized_uid)

      expect(result).to eq(model)
    end

    it 'find the model with the matching id' do
      result = model_class.find_from_uid_or_id(model.id)

      expect(result).to eq(model)
    end

    context 'with no matching model' do
      it 'returns nil' do
        result = model_class.find_from_uid_or_id('NOT-A-uid')

        expect(result).to be_nil
      end
    end
  end

  describe '.find_from_uid_or_id!' do
    let!(:model) { create_model! }
    let!(:other_model) { create_model! }

    it 'finds the model with the matching uid, ignoring all non-alphanumeric characters' do
      result = model_class.find_from_uid_or_id!(model.humanized_uid)

      expect(result).to eq(model)
    end

    it 'finds the model with the matching id' do
      result = model_class.find_from_uid_or_id!(model.id)

      expect(result).to eq(model)
    end

    context 'with no matching model' do
      it 'raises ActiveRecord::RecordNotFound' do
        expect { model_class.find_from_uid_or_id!('NOT-A-uid') }.to raise_error(ActiveRecord::RecordNotFound)
      end
    end
  end

  describe '.unhumanize_uid' do
    let(:test_value) { raise }

    subject(:unhumanize_uid) do
      model_class.unhumanize_uid(test_value)
    end

    context 'with a humanized uid' do
      let(:test_value) { 'TEMP-123-456-789' }

      it 'returns the original uid' do
        expect(unhumanize_uid).to eq('TEMP-123456789')
      end
    end

    context 'with a humanized uid for a different class' do
      let(:test_value) { 'SOMETHING-123-456-789' }

      it 'returns the original uid' do
        expect(unhumanize_uid).to eq(test_value)
      end
    end

    context 'with a nil humanized uid' do
      let(:test_value) { nil }

      it 'returns nil' do
        expect(unhumanize_uid).to be_nil
      end
    end
  end

  describe 'with joined relation where 2 or more tables contain the uid column' do
    before do
      Temping.create :parent_model do
        with_columns do |t|
          t.string :uid
        end
      end
      ParentModel.include described_class
      ParentModel.has_uid prefix: 'PARENT'
      ParentModel.has_many :child_models

      Temping.create :child_model do
        with_columns do |t|
          t.string :uid
          t.integer :parent_model_id
        end
      end
      ChildModel.include described_class
      ChildModel.has_uid prefix: 'CHILD'
      ChildModel.belongs_to :parent_model, class_name: 'ParentModel'
    end

    after do
      Temping.teardown
    end

    it 'does not raise ambiguous column error when using uid_cont scope with join' do
      parent = ParentModel.create!
      ChildModel.create!(parent_model: parent)

      expect do
        ParentModel.joins(:child_models).merge(ChildModel.uid_cont('C')).to_a
      end.not_to raise_error
    end
  end
end
