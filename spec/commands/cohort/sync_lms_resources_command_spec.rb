# frozen_string_literal: true

require 'rails_helper'

class Cohort
  describe SyncLmsResourcesCommand do
    let(:command) { described_class.new }

    describe '#sync_lms_resources_for_cohorts' do
      let!(:cohort_1) { create(:cohort, status: :opened) }
      let!(:cohort_2) { create(:cohort, status: :started) }
      let!(:cohort_3) { create(:cohort, status: :inactive) }
      let!(:remote_canvas_course_1) { create(:remote_canvas_course, core_record: cohort_1) }
      let!(:remote_canvas_course_2) { create(:remote_canvas_course, core_record: cohort_2) }
      let!(:remote_canvas_course_3) { create(:remote_canvas_course, core_record: cohort_3) }

      context 'when all cohort syncs are successful' do
        before do
          allow(Cohort::FetchModulesAndAssignmentGroupsCommand).to receive(:call!)
          allow(JsonLogger).to receive(:log)
        end

        it 'processes all cohorts and logs cohort IDs' do
          command.send(:sync_lms_resources_for_cohorts)

          expect(Cohort::FetchModulesAndAssignmentGroupsCommand).to have_received(:call!)
            .with(cohort: cohort_1)
          expect(Cohort::FetchModulesAndAssignmentGroupsCommand).to have_received(:call!)
            .with(cohort: cohort_2)
          expect(Cohort::FetchModulesAndAssignmentGroupsCommand).to have_received(:call!)
            .with(cohort: cohort_3)

          expect(JsonLogger).to have_received(:log).with(
            type: 'Cohort::SyncLmsResourcesCommand',
            cohorts_ids: contain_exactly(cohort_1.id, cohort_2.id, cohort_3.id),
          )
        end
      end

      context 'when some cohort syncs fail' do
        let(:error_message) { 'Canvas API error' }
        let(:error) { StandardError.new(error_message) }

        before do
          allow(Cohort::FetchModulesAndAssignmentGroupsCommand).to receive(:call!)
            .with(cohort: cohort_1).and_raise(error)
          allow(Cohort::FetchModulesAndAssignmentGroupsCommand).to receive(:call!)
            .with(cohort: cohort_2)
          allow(Cohort::FetchModulesAndAssignmentGroupsCommand).to receive(:call!)
            .with(cohort: cohort_3).and_raise(error)

          allow(ErrorReporter).to receive(:report)
          allow(JsonLogger).to receive(:log)
        end

        it 'continues processing all cohorts and reports errors' do
          command.send(:sync_lms_resources_for_cohorts)

          expect(Cohort::FetchModulesAndAssignmentGroupsCommand).to have_received(:call!)
            .exactly(3).times

          expect(ErrorReporter).to have_received(:report).with(
            error:,
            source: 'Cohort::SyncLmsResourcesCommand',
            cohort_key: cohort_1.key,
            message: "Failed to refresh LMS data for cohort #{cohort_1.key}",
          )

          expect(ErrorReporter).to have_received(:report).with(
            error:,
            source: 'Cohort::SyncLmsResourcesCommand',
            cohort_key: cohort_3.key,
            message: "Failed to refresh LMS data for cohort #{cohort_3.key}",
          )

          expect(JsonLogger).to have_received(:log).with(
            type: 'Cohort::SyncLmsResourcesCommand',
            cohorts_ids: contain_exactly(cohort_1.id, cohort_2.id, cohort_3.id),
          )
        end
      end

      context 'when no eligible cohorts are found' do
        before do
          allow(JsonLogger).to receive(:log)
          allow_any_instance_of(described_class).to receive(:cohorts).and_return(Cohort.none)
        end

        it 'logs empty cohorts_ids array' do
          command.send(:sync_lms_resources_for_cohorts)

          expect(JsonLogger).to have_received(:log).with(
            type: 'Cohort::SyncLmsResourcesCommand',
            cohorts_ids: [],
          )
        end
      end
    end

    describe '#sync_lms_resources_for_sections' do
      let!(:cohort_1) { create(:cohort, status: :opened) }
      let!(:cohort_2) { create(:cohort, status: :started) }
      let!(:remote_canvas_course_1) { create(:remote_canvas_course, core_record: cohort_1) }
      let!(:remote_canvas_course_2) { create(:remote_canvas_course, core_record: cohort_2) }
      let!(:section_1) { create(:section, cohort: cohort_1) }
      let!(:section_2) { create(:section, cohort: cohort_2) }
      let!(:section_3) { create(:section, cohort: cohort_1) }
      let!(:remote_canvas_section_1) { create(:remote_canvas_section, core_record: section_1) }
      let!(:remote_canvas_section_2) { create(:remote_canvas_section, core_record: section_2) }
      let!(:remote_canvas_section_3) { create(:remote_canvas_section, core_record: section_3) }

      context 'when all section syncs are successful' do
        before do
          allow(Section::AssignToAssignmentsCommand).to receive(:call!)
        end

        it 'processes all sections for the eligible cohorts' do
          command.send(:sync_lms_resources_for_sections)

          expect(Section::AssignToAssignmentsCommand).to have_received(:call!)
            .with(section: section_1)
          expect(Section::AssignToAssignmentsCommand).to have_received(:call!)
            .with(section: section_2)
          expect(Section::AssignToAssignmentsCommand).to have_received(:call!)
            .with(section: section_3)
        end
      end

      context 'when some section syncs fail' do
        let(:error_message) { 'Section sync failed' }
        let(:error) { StandardError.new(error_message) }

        before do
          allow(Section::AssignToAssignmentsCommand).to receive(:call!)
            .with(section: section_1).and_raise(error)
          allow(Section::AssignToAssignmentsCommand).to receive(:call!)
            .with(section: section_2)
          allow(Section::AssignToAssignmentsCommand).to receive(:call!)
            .with(section: section_3).and_raise(error)

          allow(ErrorReporter).to receive(:report)
        end

        it 'continues processing all sections and reports errors' do
          command.send(:sync_lms_resources_for_sections)

          expect(Section::AssignToAssignmentsCommand).to have_received(:call!)
            .exactly(3).times

          expect(ErrorReporter).to have_received(:report).with(
            error:,
            source: 'Cohort::SyncLmsResourcesCommand',
            section_uid: section_1.humanized_uid,
            message: "Failed to sync LMS resources for section #{section_1.humanized_uid}",
          )

          expect(ErrorReporter).to have_received(:report).with(
            error:,
            source: 'Cohort::SyncLmsResourcesCommand',
            section_uid: section_3.humanized_uid,
            message: "Failed to sync LMS resources for section #{section_3.humanized_uid}",
          )
        end
      end

      context 'when no eligible sections are found' do
        before do
          allow_any_instance_of(described_class).to receive(:cohorts).and_return(Cohort.none)
        end

        it 'does not call Section::AssignToAssignmentsCommand' do
          allow(Section::AssignToAssignmentsCommand).to receive(:call!)

          command.send(:sync_lms_resources_for_sections)

          expect(Section::AssignToAssignmentsCommand).not_to have_received(:call!)
        end
      end
    end

    describe '#cohorts' do
      let!(:opened_cohort) { create(:cohort, status: :opened) }
      let!(:started_cohort) { create(:cohort, status: :started) }
      let!(:ended_cohort) { create(:cohort, status: :ended) }
      let!(:extension_ended_cohort) { create(:cohort, status: :extension_period_ended) }
      let!(:inactive_cohort) { create(:cohort, status: :inactive) }
      let!(:created_cohort) { create(:cohort, status: :created) }
      let!(:closed_cohort) { create(:cohort, status: :closed) }

      let!(:canvas_course_opened) { create(:remote_canvas_course, core_record: opened_cohort) }
      let!(:canvas_course_started) { create(:remote_canvas_course, core_record: started_cohort) }
      let!(:canvas_course_ended) { create(:remote_canvas_course, core_record: ended_cohort) }
      let!(:canvas_course_extension_ended) { create(:remote_canvas_course, core_record: extension_ended_cohort) }
      let!(:canvas_course_inactive) { create(:remote_canvas_course, core_record: inactive_cohort) }

      it 'returns only cohorts with LMS_OPENED_STATUSES and canvas courses' do
        cohorts = command.send(:cohorts)

        expect(cohorts).to contain_exactly(
          opened_cohort,
          started_cohort,
          ended_cohort,
          extension_ended_cohort,
          inactive_cohort,
        )
      end

      it 'excludes cohorts without canvas courses even with LMS_OPENED_STATUSES' do
        opened_without_canvas = create(:cohort, status: :opened)

        cohorts = command.send(:cohorts)

        expect(cohorts).not_to include(opened_without_canvas)
      end

      it 'excludes cohorts with canvas courses but without LMS_OPENED_STATUSES' do
        cohorts = command.send(:cohorts)

        expect(cohorts).not_to include(created_cohort)
        expect(cohorts).not_to include(closed_cohort)
      end
    end

    describe '#sections' do
      let!(:cohort_1) { create(:cohort, status: :opened) }
      let!(:cohort_2) { create(:cohort, status: :started) }
      let!(:cohort_3) { create(:cohort, status: :created) } # not in LMS_OPENED_STATUSES
      let!(:remote_canvas_course_1) { create(:remote_canvas_course, core_record: cohort_1) }
      let!(:remote_canvas_course_2) { create(:remote_canvas_course, core_record: cohort_2) }
      let!(:remote_canvas_course_3) { create(:remote_canvas_course, core_record: cohort_3) }

      let!(:section_1) { create(:section, cohort: cohort_1) }
      let!(:section_2) { create(:section, cohort: cohort_1) }
      let!(:section_3) { create(:section, cohort: cohort_2) }
      let!(:section_4) { create(:section, cohort: cohort_3) }
      let!(:remote_canvas_section_1) { create(:remote_canvas_section, core_record: section_1) }
      let!(:remote_canvas_section_2) { create(:remote_canvas_section, core_record: section_2) }
      let!(:remote_canvas_section_3) { create(:remote_canvas_section, core_record: section_3) }
      let!(:remote_canvas_section_4) { create(:remote_canvas_section, core_record: section_4) }

      it 'returns sections for cohorts with LMS_OPENED_STATUSES and canvas courses' do
        sections = command.send(:sections)

        expect(sections).to contain_exactly(
          section_1,
          section_2,
          section_3,
        )
      end

      it 'excludes sections from cohorts without LMS_OPENED_STATUSES' do
        sections = command.send(:sections)

        expect(sections).not_to include(section_4)
      end
    end
  end
end
