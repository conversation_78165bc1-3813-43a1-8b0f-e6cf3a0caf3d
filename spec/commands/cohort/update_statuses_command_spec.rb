# frozen_string_literal: true

require 'rails_helper'

class Cohort
  describe UpdateStatusesCommand do
    describe '#call!' do
      around do |example|
        Time.use_zone('Eastern Time (US & Canada)') { example.run }
      end

      let(:scope) { Cohort.all }
      subject(:call!) { described_class.call!(type_to_update:, scope:) }

      context 'with type_to_update: :all' do
        let(:type_to_update) { :all }

        context 'when today is between any date attribute ranges' do
          let!(:created_cohort) { create(:cohort, :created, status: :closed) }
          let!(:opened_cohort) { create(:cohort, :opened, status: :created) }
          let!(:invalid_opened_cohort) { create(:cohort, :opened, status: :created) }
          let!(:started_cohort) { create(:cohort, :started, status: :created) }
          let!(:ended_cohort) { create(:cohort, :ended, status: :created) }
          let!(:extension_period_ended_cohort) { create(:cohort, :extension_period_ended, status: :created) }
          let!(:inactive_cohort) { create(:cohort, :inactive) }
          let!(:closed_cohort) { create(:cohort, :closed, status: :created) }

          let!(:opened_cohort_enrollment) { create(:enrollment, cohort: opened_cohort) }
          let!(:paused_enrollment) { create(:enrollment, :paused, cohort: opened_cohort) }

          before do
            allow(Enrollment::ActivateCommand).to receive(:new).and_return(instance_double(Enrollment::ActivateCommand, call!: true))
            create(:remote_canvas_course, core_record: opened_cohort)

            # Set up ALL sections in opened_cohort with required URLs and remote_canvas_section
            opened_cohort.sections.each do |section|
              section.update!(
                chat_join_url: 'http://join.example.com',
                chat_workspace_key: 'http://workspace.example.com',
                conferencing_url: 'http://conf.example.com',
              )
              create(:remote_canvas_section, core_record: section)
            end
          end


          it 'updates the statuses that trigger off start dates' do
            call!

            expect(created_cohort.reload.status).to eq('created')
            expect(opened_cohort.reload.status).to eq('opened')
            expect(started_cohort.reload.status).to eq('started')
            expect(ended_cohort.reload.status).to eq('ended')
            expect(extension_period_ended_cohort.reload.status).to eq('extension_period_ended')
            expect(closed_cohort.reload.status).to eq('closed')

            # inactive cohorts should not be updated by this command, they are handled in another job
            expect(inactive_cohort.reload.status).to eq('inactive')

            # does not open the cohorts that do not satisfy the required conditions
            expect(invalid_opened_cohort.reload.status).to eq('created')
          end

          it 'activates enrollments for opened cohorts' do
            call!

            enrollment = opened_cohort_enrollment
            expect(Enrollment::ActivateCommand).to have_received(:new).with(enrollment:, notify_in: Cohort::OpenCommand::EMAIL_DELAY)
          end

          it 'does not activate paused enrollments' do
            call!

            expect(Enrollment::ActivateCommand).not_to have_received(:new)
              .with(enrollment: paused_enrollment, notify_in: Cohort::OpenCommand::EMAIL_DELAY)
          end
        end
      end

      context 'with type_to_update: :starts' do
        let(:type_to_update) { :starts }

        context 'when today is between any date attribute ranges' do
          let!(:created_cohort) { create(:cohort, :created, status: :closed) }
          let!(:opened_cohort) { create(:cohort, :opened, status: :created) }
          let!(:invalid_opened_cohort) { create(:cohort, :opened, status: :created) }
          let!(:started_cohort) { create(:cohort, :started, status: :created) }
          let!(:ended_cohort) { create(:cohort, :ended, status: :created) }
          let!(:extension_period_ended_cohort) { create(:cohort, :extension_period_ended, status: :created) }
          let!(:inactive_cohort) { create(:cohort, :inactive) }
          let!(:closed_cohort) { create(:cohort, :closed, status: :created) }
          let!(:opened_cohort_section) do
            create(:section, cohort: opened_cohort,
              chat_join_url: 'http://join.example.com',
              chat_workspace_key: 'http://workspace.example.com',
              conferencing_url: 'http://conf.example.com',
            )
          end

          before do
            opened_cohort.sections << opened_cohort_section
            create(:remote_canvas_course, core_record: opened_cohort)
            create(:remote_canvas_section, core_record: opened_cohort_section)
          end


          it 'updates the statuses that trigger off start dates' do
            call!

            # updates statuses that are based off :starts date attributes
            expect(created_cohort.reload.status).to eq('created')
            expect(opened_cohort.reload.status).to eq('opened')
            expect(started_cohort.reload.status).to eq('started')

            # does not update cohorts that are based off :ends date attributes
            expect(ended_cohort.reload.status).to eq('created')
            expect(extension_period_ended_cohort.reload.status).to eq('created')
            expect(closed_cohort.reload.status).to eq('created')

            # inactive cohorts should not be updated by this command, they are handled in another job
            expect(inactive_cohort.reload.status).to eq('inactive')

            # does not open the cohorts that do not satisfy the required conditions
            expect(invalid_opened_cohort.reload.status).to eq('created')
          end

          context 'when given a scope' do
            let(:scope) { Cohort.where(id: [started_cohort.id, ended_cohort.id]) }

            it 'only updates the cohorts within the scope' do
              call!


              # does not update cohorts not in the scope
              expect(created_cohort.reload.status).to eq('closed')
              expect(opened_cohort.reload.status).to eq('created')
              expect(extension_period_ended_cohort.reload.status).to eq('created')
              expect(closed_cohort.reload.status).to eq('created')
              expect(ended_cohort.reload.status).to eq('created')

              # updates :starts cohorts in the scope
              expect(started_cohort.reload.status).to eq('started')

              # inactive cohorts should not be updated by this command, they are handled in another job
              expect(inactive_cohort.reload.status).to eq('inactive')
            end
          end
        end

        context 'when yesterday is right on a date attribute' do
          let!(:cohort) { create(:cohort, :opened, starts_on: Time.zone.today - 1) }

          it 'does update the status' do
            call!

            expect(cohort.reload.status).to eq('started')
          end
        end

        context 'when today is right on a date attribute' do
          let!(:cohort) { create(:cohort, :opened, starts_on: Time.zone.today) }

          it 'updates the cohort to the next status' do
            call!

            expect(cohort.reload.status).to eq('started')
          end
        end

        context 'when tomorrow is right on a date attribute' do
          let!(:cohort) { create(:cohort, :opened, starts_on: Time.zone.today + 1) }

          it 'does not update the status' do
            call!

            expect(cohort.reload.status).to eq('opened')
          end
        end
      end

      context 'with type_to_update: :ends' do
        let(:type_to_update) { :ends }

        context 'when yesterday is between any date attribute ranges' do
          let!(:created_cohort) { create(:cohort, :created, status: :closed) }
          let!(:opened_cohort) { create(:cohort, :opened, status: :created) }
          let!(:started_cohort) { create(:cohort, :started, status: :created) }
          let!(:ended_cohort) { create(:cohort, :ended, status: :created) }
          let!(:extension_period_ended_cohort) { create(:cohort, :extension_period_ended, status: :created) }
          let!(:inactive_cohort) { create(:cohort, :inactive) }
          let!(:closed_cohort) { create(:cohort, :closed, status: :created) }
          let!(:opened_cohort_section) do
            create(:section, cohort: opened_cohort,
              chat_join_url: 'http://join.example.com',
              chat_workspace_key: 'http://workspace.example.com',
              conferencing_url: 'http://conf.example.com',
            )
          end

          before do
            opened_cohort.sections << opened_cohort_section
            create(:remote_canvas_course, core_record: opened_cohort)
            create(:remote_canvas_section, core_record: opened_cohort_section)
          end


          it 'updates the statuses that trigger off end dates' do
            call!

            # does not update statuses that are based off :starts date attributes
            expect(created_cohort.reload.status).to eq('closed')
            expect(opened_cohort.reload.status).to eq('created')
            expect(started_cohort.reload.status).to eq('created')

            expect(ended_cohort.reload.status).to eq('ended')
            expect(extension_period_ended_cohort.reload.status).to eq('extension_period_ended')
            expect(closed_cohort.reload.status).to eq('closed')

            # inactive cohorts should not be updated by this command, they are handled in another job
            expect(inactive_cohort.reload.status).to eq('inactive')
          end

          context 'when given a scope' do
            let(:scope) { Cohort.where(id: [started_cohort.id, ended_cohort.id]) }

            it 'only updates the cohorts within the scope' do
              call!


              # does not update statuses that are based off :starts date attributes
              expect(created_cohort.reload.status).to eq('closed')
              expect(opened_cohort.reload.status).to eq('created')
              expect(started_cohort.reload.status).to eq('created')

              # does not update cohorts not in the scope
              expect(extension_period_ended_cohort.reload.status).to eq('created')
              expect(closed_cohort.reload.status).to eq('created')

              # updates :ends cohorts in the scope
              expect(ended_cohort.reload.status).to eq('ended')

              # inactive cohorts should not be updated by this command, they are handled in another job
              expect(inactive_cohort.reload.status).to eq('inactive')
            end
          end
        end

        context 'when yesterday is right on a date attribute' do
          let!(:cohort) { create(:cohort, :started, status: :created, starts_on: Time.zone.today - 10.weeks.in_days, ends_on: Time.zone.yesterday) }

          it 'updates the cohort to the next status' do
            call!

            expect(cohort.reload.status).to eq('ended')
          end
        end

        context 'when today is right on a date attribute' do
          let!(:cohort) { create(:cohort, :started, status: :created, starts_on: Time.zone.today - 10.weeks.in_days, ends_on: Time.zone.today) }

          it 'does not update the status' do
            call!

            expect(cohort.reload.status).to eq('created')
          end
        end

        context 'when tomorrow is right on a date attribute' do
          let!(:cohort) { create(:cohort, :started, status: :created, starts_on: Time.zone.today - 10.weeks.in_days, ends_on: Time.zone.today + 1) }

          it 'does not update the status' do
            call!

            expect(cohort.reload.status).to eq('created')
          end
        end
      end

      context 'with unknown type_to_update' do
        let(:type_to_update) { :unknown }

        it 'raises an error' do
          expect { call! }.to raise_error(ArgumentError)
        end
      end
    end

    describe 'selected_cohorts filtering for :opened status' do
      let(:type_to_update) { :all }
      let(:scope) { Cohort.all }
      subject(:call!) { described_class.call!(type_to_update:, scope:) }

      context 'when the cohort has a valid section with all required URL fields, remote_canvas_course, and matching remote_resource' do
        let!(:cohort) { create(:cohort, :opened, status: :created) }
        let!(:section) do
          create(:section, cohort:,
            chat_join_url: 'http://join.example.com',
            chat_workspace_key: 'ws-key',
            conferencing_url: 'http://conf.example.com',
          )
        end

        before do
          create(:remote_canvas_course, core_record: cohort)
          create(:remote_canvas_section, core_record: section)
        end

        it 'updates the cohort status to :opened' do
          call!
          expect(cohort.reload.status).to eq('opened')
        end
      end

      context 'when the cohort has a section missing one of the required URL fields' do
        let!(:cohort) { create(:cohort, :opened, status: :created) }
        let!(:section) do
          create(:section, cohort:,
            chat_join_url: nil, # missing join url
            chat_workspace_key: 'ws-key',
            conferencing_url: 'http://conf.example.com',
          )
        end

        before do
          create(:remote_canvas_course, core_record: cohort)
          create(:remote_canvas_section, core_record: section)
        end

        it 'does not update the cohort status' do
          call!
          expect(cohort.reload.status).to eq('created')
        end
      end

      context 'when the cohort has multiple sections and not all sections have a remote_resource match' do
        let!(:cohort) { create(:cohort, :opened, status: :created) }
        let!(:valid_section) do
          create(:section, cohort:,
            chat_join_url: 'http://join.example.com',
            chat_workspace_key: 'ws-key',
            conferencing_url: 'http://conf.example.com',
          )
        end
        let!(:invalid_section) do
          # This section is valid in terms of URLs but will have no remote_resource
          create(:section, cohort:,
            chat_join_url: 'http://join.example.com',
            chat_workspace_key: 'ws-key',
            conferencing_url: 'http://conf.example.com',
          )
        end

        before do
          create(:remote_canvas_course, core_record: cohort)
          # Only create remote_resource for the valid_section
          create(:remote_canvas_section, core_record: valid_section)
        end

        it 'does not update the cohort status due to incomplete remote_resources join' do
          call!
          expect(cohort.reload.status).to eq('created')
        end
      end

      context 'when the cohort is missing the remote_canvas_course record' do
        let!(:cohort) { create(:cohort, :opened, status: :created) }
        let!(:section) do
          create(:section, cohort:,
            chat_join_url: 'http://join.example.com',
            chat_workspace_key: 'ws-key',
            conferencing_url: 'http://conf.example.com',
          )
        end

        before do
          # Do not create a remote_canvas_course for this cohort.
          create(:remote_canvas_section, core_record: section)
        end

        it 'does not update the cohort status because the join fails' do
          call!
          expect(cohort.reload.status).to eq('created')
        end
      end
    end
  end
end
