# frozen_string_literal: true

require 'rails_helper'

class Cohort
  describe OpenCommand do
    let(:cohort) { create(:cohort, status:) }
    let(:status) { :created }

    describe '.openable?' do
      subject(:openable) { described_class.openable?(cohort:) }

      before do
        # Update the actual cohort record instead of mocking
        cohort.update!(lms_opens_on:)

        # Set up required resources for cohort to be openable
        create(:remote_canvas_course, core_record: cohort)

        # Ensure cohort has at least one section and set it up properly
        create(:section, cohort:) if cohort.sections.empty?

        # Set up ALL sections in cohort with required URLs and remote_canvas_section
        cohort.sections.each do |section|
          section.update!(
            chat_join_url: 'http://join.example.com',
            chat_workspace_key: 'http://workspace.example.com',
            conferencing_url: 'http://conf.example.com',
          )
          create(:remote_canvas_section, core_record: section)
        end
      end

      context 'when cohort is created and lms_opens_on is in the past' do
        let(:lms_opens_on) { 1.day.ago }

        it { is_expected.to be true }
      end

      context 'when cohort is not in created status' do
        let(:status) { :opened }
        let(:lms_opens_on) { 1.day.ago }

        it { is_expected.to be false }
      end

      context 'when lms_opens_on is in the future' do
        let(:lms_opens_on) { 1.day.from_now }

        it { is_expected.to be false }
      end
    end

    describe '#initialize' do
      it 'assigns the cohort and notify_in values' do
        command = described_class.new(cohort:, notify_in: 5.minutes)
        expect(command.cohort).to eq(cohort)
        expect(command.notify_in).to eq(5.minutes)
      end

      it 'defaults notify_in to EMAIL_DELAY when not provided' do
        command = described_class.new(cohort:)
        expect(command.cohort).to eq(cohort)
        expect(command.notify_in).to eq(described_class::EMAIL_DELAY)
      end
    end

    describe '#call!' do
      subject(:command) { described_class.new(cohort:, notify_in:) }

      let(:notify_in) { 0 }
      let!(:enrollment) { create(:enrollment, cohort:) }

      before do
        allow(Enrollment::ActivateCommand).to receive(:call!)
        allow(JsonLogger).to receive(:log)

        # Make sure cohort has lms_opens_on in the past so it's openable
        cohort.update!(lms_opens_on: 1.day.ago)

        # Set up required resources for cohort to be openable
        create(:remote_canvas_course, core_record: cohort)

        # Ensure cohort has at least one section and set it up properly
        create(:section, cohort:) if cohort.sections.empty?

        # Set up ALL sections in cohort with required URLs and remote_canvas_section
        cohort.sections.each do |section|
          section.update!(
            chat_join_url: 'http://join.example.com',
            chat_workspace_key: 'http://workspace.example.com',
            conferencing_url: 'http://conf.example.com',
          )
          create(:remote_canvas_section, core_record: section)
        end
      end

      it 'updates cohort status to opened' do
        expect { command.call! }
          .to change { cohort.reload.status }
          .from('created')
          .to('opened')
      end

      it 'activates primary enrollments' do
        command.call!

        expect(Enrollment::ActivateCommand).to have_received(:call!).with(enrollment:, notify_in:)
      end

      it 'logs the open_cohort action' do
        command.call!

        expect(JsonLogger).to have_received(:log).with(
          type: 'Cohort::OpenCommand',
          action: 'open_cohort',
          cohort_id: cohort.id,
        )
      end

      context 'when enrollment activation fails' do
        let(:error) { StandardError.new('Activation failed') }

        before do
          allow(Enrollment::ActivateCommand).to receive(:call!).and_raise(error)
          allow(ErrorReporter).to receive(:report)
        end

        it 'reports the error' do
          command.call!

          expect(ErrorReporter).to have_received(:report).with(
            error:,
            source: described_class.name,
            severity: :fatal,
            enrollment_uid: enrollment.humanized_uid,
            message: "Failed to activate enrollment: #{error.message}",
          )
        end

        it 'continues processing despite errors' do
          expect { command.call! }.not_to raise_error
        end
      end

      context 'when cohort is not openable' do
        before do
          allow(ErrorReporter).to receive(:report)
          # Make cohort not openable by setting lms_opens_on to future
          cohort.update!(lms_opens_on: 1.day.from_now)
        end

        it 'reports the error with ErrorReporter' do
          command.call!

          expect(ErrorReporter).to have_received(:report).with(
            error: an_instance_of(StandardError),
            source: described_class.name,
            severity: :warning,
            cohort_id: cohort.id,
            message: "Attempted to open cohort #{cohort.id} but it is not openable",
          )
        end

        it 'does not update cohort status' do
          expect { command.call! }.not_to(change { cohort.reload.status })
        end

        it 'does not activate enrollments' do
          command.call!

          expect(Enrollment::ActivateCommand).not_to have_received(:call!)
        end
      end
    end
  end
end
