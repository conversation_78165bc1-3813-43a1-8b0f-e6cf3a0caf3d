# frozen_string_literal: true

require 'rails_helper'

module LearningDelivery
  class Task
    describe CreateRemoveLearnerTaskCommand, type: :command do
      describe '#call!' do
        let(:enrollment) { create(:enrollment) }
        let(:command) { described_class.new(enrollment:) }
        let(:presenter) { instance_double(LearningDelivery::Task::RemoveLearnerPresenter) }
        let(:learner) { create(:learner) }
        let(:due_date) { 1.day.from_now }
        let(:title) { 'Task Title' }
        let(:description) { 'Task Description' }
        let(:recommendation) { 'Task Recommendation' }
        let(:reason) { 'Task Reason' }

        before do
          allow(command).to receive(:presenter).and_return(presenter)
          allow(presenter).to receive_messages(learner:, due_date:, title:, description:,
            recommendation:, reason:,
          )
        end

        context 'when learning_delivery_tasks setting is enabled' do
          before do
            create(:setting, name: 'development_flags', value: { learning_delivery_tasks: { remove_learner: "present" } })
          end

          it 'creates a remove learner task' do
            expect do
              command.call!
            end.to change { LearningDelivery::Task::RemoveLearner.count }.by(1)


            task = LearningDelivery::Task::RemoveLearner.last

            expect(task).to have_attributes(
              resource: enrollment,
              status: 'created',
              type: 'LearningDelivery::Task::RemoveLearner',
              title:,
              description:,
              recommendation:,
              reason:,
            )
            expect(task.due_at).to be_within(1.second).of(due_date)
          end
        end

        context 'when learning_delivery_tasks setting is disabled' do
          it 'does not create a remove learner task' do
            expect do
              command.call!
            end.not_to(change { LearningDelivery::Task::RemoveLearner.count })
          end
        end
      end

      describe '#callable?' do
        let(:enrollment) { create(:enrollment) }
        let(:command) { described_class.new(enrollment:) }

        context 'when all conditions are met' do
          before do
            create(:setting, name: 'development_flags', value: { learning_delivery_tasks: { remove_learner: "present" } })
          end

          it 'returns true' do
            expect(command.callable?).to be true
          end
        end

        context 'when there are open tasks' do
          before do
            create(:setting, name: 'development_flags', value: { learning_delivery_tasks: { remove_learner: "present" } })
            create(:learning_delivery_task, :remove_learner, resource: enrollment, status: :created)
          end

          it 'returns false' do
            expect(command.callable?).to be false
          end
        end

        context 'when learning_delivery_tasks setting is disabled' do
          it 'returns false' do
            expect(command.callable?).to be false
          end
        end
      end

      describe '#no_open_tasks?' do
        let(:enrollment) { create(:enrollment) }
        let(:command) { described_class.new(enrollment:) }

        context 'when there are no open tasks' do
          it 'returns true' do
            expect(command.no_open_tasks?).to be true
          end
        end

        context 'when there are open tasks' do
          before do
            create(:learning_delivery_task, :remove_learner, resource: enrollment, status: :created)
          end

          it 'returns false' do
            expect(command.no_open_tasks?).to be false
          end
        end
      end

      describe '#presenter' do
        let(:enrollment) { create(:enrollment) }
        let(:command) { described_class.new(enrollment:) }

        it 'returns a RemoveLearnerPresenter' do
          presenter_instance = instance_double(LearningDelivery::Task::RemoveLearnerPresenter)
          allow(LearningDelivery::Task::RemoveLearnerPresenter).to receive(:new).with(resource: enrollment).and_return(presenter_instance)

          expect(command.presenter).to eq(presenter_instance)
          expect(LearningDelivery::Task::RemoveLearnerPresenter).to have_received(:new).with(resource: enrollment)
        end
      end
    end
  end
end
