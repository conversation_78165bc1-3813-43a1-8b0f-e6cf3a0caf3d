# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Remote::Hubspot::BatchPublishCommand, type: :command do
  let(:client) { instance_double(HubspotClient) }
  let(:collection) { create_list(:remote_hubspot_deal, 2) }
  let(:payload) { { 'properties' => { 'key' => 'value' } } }
  let(:command) { described_class.new(collection:, payload:) }

  before do
    allow(HubspotClient).to receive(:new).and_return(client)
    allow(client).to receive(:post)
  end

  describe '#call!' do
    context 'when collection is empty' do
      let(:collection) { [] }

      it 'does not make a post request' do
        command.call!
        expect(client).not_to have_received(:post)
      end
    end

    context 'when collection is not empty' do
      it 'makes a post request with the correct parameters' do
        command.call!
        expect(client).to have_received(:post).with("/objects/deals/batch/upsert", json: { inputs: anything })
      end
    end
  end

  describe '#inputs' do
    context 'when payload is a Hash' do
      it 'returns the correct inputs' do
        inputs = command.send(:inputs)
        expect(inputs).to all(include(properties: payload['properties']))
      end
    end

    context 'when payload is a Proc' do
      let(:payload) { proc { |resource| { 'properties' => { 'key' => resource.created_at } } } }

      it 'returns the correct inputs' do
        inputs = command.send(:inputs)
        expect(inputs).to all(include(properties: { 'key' => anything }))
      end
    end

    context 'when payload is a Symbol' do
      let(:payload) { :default_payload }
      before do
        allow(collection.first).to receive(:default_payload).and_return('properties' => { 'key' => 'value' })
        allow(collection.last).to receive(:default_payload).and_return('properties' => { 'key' => 'value' })
      end

      it 'returns the correct inputs' do
        inputs = command.send(:inputs)
        expect(inputs).to all(include(properties: { 'key' => 'value' }))
      end
    end

    context 'when payload is nil' do
      before do
        allow(collection.first).to receive(:default_payload).and_return('properties' => { 'key' => 'value' })
        allow(collection.last).to receive(:default_payload).and_return('properties' => { 'key' => 'value' })
      end

      it 'returns the correct inputs' do
        inputs = command.send(:inputs)
        expect(inputs).to all(include(properties: { 'key' => 'value' }))
      end
    end

    context 'when collection contains duplicate ids' do
      let(:duplicate_id) { 'duplicate-id' }
      let(:collection) do
        [
          instance_double(Remote::Hubspot::Deal, alternate_key_property: 'dealId', alternate_key: duplicate_id,
            default_payload: { 'properties' => { 'key' => 'value1' } }, remote_object: 'deal',
          ),
          instance_double(Remote::Hubspot::Deal, alternate_key_property: 'dealId', alternate_key: duplicate_id,
            default_payload: { 'properties' => { 'key' => 'value2' } }, remote_object: 'deal',
          ),
        ]
      end
      let(:payload) { nil }

      it 'deduplicates inputs by id' do
        inputs = command.send(:inputs)
        expect(inputs.size).to eq(1)
        expect(inputs.first[:id]).to eq(duplicate_id)
      end
    end
  end
end
