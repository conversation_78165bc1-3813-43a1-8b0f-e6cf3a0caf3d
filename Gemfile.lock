GIT
  remote: ***********************:zip-learning/db-snapshot.git
  revision: c3f8ce5d465a353e1f3b172fc63391f27054844f
  tag: v1.1.4
  specs:
    db-snapshot (1.1.4)
      activerecord
      activesupport
      aws-sdk-cloudwatch (~> 1.0)
      aws-sdk-ec2 (~> 1.0)
      aws-sdk-ecs (~> 1.0)
      aws-sdk-rds (~> 1.0)
      aws-sdk-s3 (~> 1.0)
      aws-sdk-ssm (~> 1.0)
      net-scp
      net-ssh
      pastel (~> 0)
      pg (~> 1.0)
      rollbar
      tty-progressbar (~> 0)
      tty-prompt (~> 0)
      tty-spinner (~> 0)

GIT
  remote: ***********************:zip-learning/parameter-store-rails.git
  revision: 021bef5dc0ecf7f9988f271dd26abd0c009d57f6
  tag: v0.0.10
  specs:
    parameter-store-rails (0.0.10)
      aws-sdk-ssm (~> 1)
      dotenv-rails (~> 3.1)
      lockfile (~> 2.1)
      rails (~> 8.0)

GIT
  remote: ***********************:zip-learning/rails-remote-access.git
  revision: 193e6ab57dd054200fa2221c3b83d75640ce6be2
  tag: v0.0.18
  specs:
    rails-remote-access (0.0.18)
      actionview (~> 8.0)
      aws-sdk-ecr (~> 1.0)
      aws-sdk-ecs (~> 1.0)
      aws-sdk-rds (~> 1.0)
      aws-sdk-s3 (~> 1.0)
      aws-sdk-ssm (~> 1.0)
      ostruct (~> 0)
      pastel (~> 0)
      tty-prompt (~> 0)
      tty-spinner (~> 0)
      tty-table (~> 0)

GIT
  remote: https://github.com/cswilliams/puma-cloudwatch.git
  revision: ebd5c4ed50580e87ba8c39ed848e27f638d6bdba
  tag: v0.5.3
  specs:
    puma-cloudwatch (0.5.3)
      aws-sdk-cloudwatch (~> 1)
      base64
      concurrent-ruby (~> 1)
      puma
      rexml

GEM
  remote: https://rubygems.org/
  specs:
    ace-rails-ap (4.5)
    actioncable (8.0.2)
      actionpack (= 8.0.2)
      activesupport (= 8.0.2)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (8.0.2)
      actionpack (= 8.0.2)
      activejob (= 8.0.2)
      activerecord (= 8.0.2)
      activestorage (= 8.0.2)
      activesupport (= 8.0.2)
      mail (>= 2.8.0)
    actionmailer (8.0.2)
      actionpack (= 8.0.2)
      actionview (= 8.0.2)
      activejob (= 8.0.2)
      activesupport (= 8.0.2)
      mail (>= 2.8.0)
      rails-dom-testing (~> 2.2)
    actionpack (8.0.2)
      actionview (= 8.0.2)
      activesupport (= 8.0.2)
      nokogiri (>= 1.8.5)
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actiontext (8.0.2)
      actionpack (= 8.0.2)
      activerecord (= 8.0.2)
      activestorage (= 8.0.2)
      activesupport (= 8.0.2)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (8.0.2)
      activesupport (= 8.0.2)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    active_material (1.5.2)
    active_storage_validations (2.0.3)
      activejob (>= 6.1.4)
      activemodel (>= 6.1.4)
      activestorage (>= 6.1.4)
      activesupport (>= 6.1.4)
      marcel (>= 1.0.3)
    activeadmin (3.3.0)
      arbre (~> 1.2, >= 1.2.1)
      csv
      formtastic (>= 3.1)
      formtastic_i18n (>= 0.4)
      inherited_resources (~> 1.7)
      jquery-rails (>= 4.2)
      kaminari (>= 1.2.1)
      railties (>= 6.1)
      ransack (>= 4.0)
    activeadmin_addons (1.10.2)
      active_material (~> 1.5)
      railties
      redcarpet
      require_all
      sassc
      sassc-rails
      xdan-datetimepicker-rails (~> 2.5.1)
    activeadmin_json_editor (0.0.10)
      ace-rails-ap
      railties (>= 3.0)
    activejob (8.0.2)
      activesupport (= 8.0.2)
      globalid (>= 0.3.6)
    activemodel (8.0.2)
      activesupport (= 8.0.2)
    activerecord (8.0.2)
      activemodel (= 8.0.2)
      activesupport (= 8.0.2)
      timeout (>= 0.4.0)
    activestorage (8.0.2)
      actionpack (= 8.0.2)
      activejob (= 8.0.2)
      activerecord (= 8.0.2)
      activesupport (= 8.0.2)
      marcel (~> 1.0)
    activesupport (8.0.2)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
      uri (>= 0.13.1)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    affirm-ruby (1.2.0)
      faraday
      virtus (~> 1.0, >= 1.0.0)
    annotaterb (4.17.0)
      activerecord (>= 6.0.0)
      activesupport (>= 6.0.0)
    arbre (1.7.0)
      activesupport (>= 3.0.0)
      ruby2_keywords (>= 0.0.2)
    ast (2.4.3)
    aws-eventstream (1.4.0)
    aws-partitions (1.1126.0)
    aws-sdk-cloudfront (1.119.0)
      aws-sdk-core (~> 3, >= 3.225.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-cloudwatch (1.116.0)
      aws-sdk-core (~> 3, >= 3.225.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-core (3.226.3)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.992.0)
      aws-sigv4 (~> 1.9)
      base64
      jmespath (~> 1, >= 1.6.1)
      logger
    aws-sdk-ec2 (1.531.0)
      aws-sdk-core (~> 3, >= 3.225.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-ecr (1.102.0)
      aws-sdk-core (~> 3, >= 3.216.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-ecs (1.193.0)
      aws-sdk-core (~> 3, >= 3.225.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-kms (1.106.0)
      aws-sdk-core (~> 3, >= 3.225.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-rds (1.280.0)
      aws-sdk-core (~> 3, >= 3.225.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-s3 (1.192.0)
      aws-sdk-core (~> 3, >= 3.225.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.5)
    aws-sdk-ssm (1.196.0)
      aws-sdk-core (~> 3, >= 3.225.0)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.12.1)
      aws-eventstream (~> 1, >= 1.0.2)
    axiom-types (0.1.1)
      descendants_tracker (~> 0.0.4)
      ice_nine (~> 0.11.0)
      thread_safe (~> 0.3, >= 0.3.1)
    base64 (0.3.0)
    bcrypt (3.1.20)
    benchmark (0.4.1)
    bigdecimal (3.2.2)
    bindex (0.8.1)
    bootsnap (1.18.4)
      msgpack (~> 1.2)
    builder (3.3.0)
    bullet (8.0.6)
      activesupport (>= 3.0.0)
      uniform_notifier (~> 1.11)
    byebug (12.0.0)
    cancancan (3.6.1)
    capybara (3.40.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.11)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    chartkick (5.1.5)
    childprocess (5.1.0)
      logger (~> 1.5)
    coderay (1.1.3)
    coercible (1.0.0)
      descendants_tracker (~> 0.0.1)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.3)
    crack (1.0.0)
      bigdecimal
      rexml
    crass (1.0.6)
    cronex (0.15.0)
      tzinfo
      unicode (>= *******)
    csv (3.3.4)
    date (3.4.1)
    debug (1.11.0)
      irb (~> 1.10)
      reline (>= 0.3.8)
    declarative (0.0.20)
    descendants_tracker (0.0.4)
      thread_safe (~> 0.3, >= 0.3.1)
    devise (4.9.4)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    diff-lcs (1.6.2)
    dotenv (3.1.8)
    dotenv-rails (3.1.8)
      dotenv (= 3.1.8)
      railties (>= 6.1)
    down (5.4.2)
      addressable (~> 2.8)
    drb (2.2.3)
    equalizer (0.0.11)
    erb (5.0.2)
    erubi (1.13.1)
    et-orbi (1.2.11)
      tzinfo
    event_stream_parser (1.0.0)
    factory_bot (6.5.4)
      activesupport (>= 6.1.0)
    factory_bot_rails (6.5.0)
      factory_bot (~> 6.5)
      railties (>= 6.1.0)
    faraday (2.13.1)
      faraday-net_http (>= 2.0, < 3.5)
      json
      logger
    faraday-multipart (1.1.0)
      multipart-post (~> 2.0)
    faraday-net_http (3.4.0)
      net-http (>= 0.5.0)
    ffaker (2.24.0)
    ffi (1.17.1-aarch64-linux-gnu)
    ffi (1.17.1-aarch64-linux-musl)
    ffi (1.17.1-arm-linux-gnu)
    ffi (1.17.1-arm64-darwin)
    ffi (1.17.1-x86-linux-gnu)
    ffi (1.17.1-x86_64-darwin)
    ffi (1.17.1-x86_64-linux-gnu)
    ffi (1.17.1-x86_64-linux-musl)
    font-awesome-sass (6.7.2)
      sassc (~> 2.0)
    formtastic (5.0.0)
      actionpack (>= 6.0.0)
    formtastic_i18n (0.7.0)
    fugit (1.11.1)
      et-orbi (~> 1, >= 1.2.11)
      raabro (~> 1.4)
    geocoder (1.8.5)
      base64 (>= 0.1.0)
      csv (>= 3.0.0)
    globalid (1.2.1)
      activesupport (>= 6.1)
    google-apis-bigquery_v2 (0.85.0)
      google-apis-core (>= 0.15.0, < 2.a)
    google-apis-calendar_v3 (0.47.0)
      google-apis-core (>= 0.15.0, < 2.a)
    google-apis-core (0.17.0)
      addressable (~> 2.5, >= 2.5.1)
      googleauth (~> 1.9)
      httpclient (>= 2.8.3, < 3.a)
      mini_mime (~> 1.0)
      mutex_m
      representable (~> 3.0)
      retriable (>= 2.0, < 4.a)
    google-apis-drive_v3 (0.65.0)
      google-apis-core (>= 0.15.0, < 2.a)
    google-apis-forms_v1 (0.18.0)
      google-apis-core (>= 0.15.0, < 2.a)
    google-cloud-bigquery (1.52.1)
      bigdecimal (~> 3.0)
      concurrent-ruby (~> 1.0)
      google-apis-bigquery_v2 (~> 0.71)
      google-apis-core (~> 0.13)
      google-cloud-core (~> 1.6)
      googleauth (~> 1.9)
      mini_mime (~> 1.0)
    google-cloud-core (1.8.0)
      google-cloud-env (>= 1.0, < 3.a)
      google-cloud-errors (~> 1.0)
    google-cloud-env (2.3.0)
      base64 (~> 0.2)
      faraday (>= 1.0, < 3.a)
    google-cloud-errors (1.5.0)
    google-logging-utils (0.2.0)
    googleauth (1.14.0)
      faraday (>= 1.0, < 3.a)
      google-cloud-env (~> 2.2)
      google-logging-utils (~> 0.1)
      jwt (>= 1.4, < 3.0)
      multi_json (~> 1.11)
      os (>= 0.9, < 2.0)
      signet (>= 0.16, < 2.a)
    has_scope (0.8.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
    hashdiff (1.1.2)
    hashie (5.0.0)
    hiredis (0.6.3)
    hiredis-client (0.25.1)
      redis-client (= 0.25.1)
    html_to_plain_text (1.0.5)
      nokogiri (>= 1.4.0)
    http-2 (1.1.1)
    httpclient (2.9.0)
      mutex_m
    httpx (1.5.1)
      http-2 (>= 1.0.0)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    ice_nine (0.11.2)
    image_processing (1.14.0)
      mini_magick (>= 4.9.5, < 6)
      ruby-vips (>= 2.0.17, < 3)
    importmap-rails (2.1.0)
      actionpack (>= 6.0.0)
      activesupport (>= 6.0.0)
      railties (>= 6.0.0)
    indefinite_article (0.2.5)
      activesupport
    inherited_resources (1.14.0)
      actionpack (>= 6.0)
      has_scope (>= 0.6)
      railties (>= 6.0)
      responders (>= 2)
    io-console (0.8.1)
    irb (1.15.2)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jbuilder (2.13.0)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    jmespath (1.6.2)
    jquery-rails (4.6.0)
      rails-dom-testing (>= 1, < 3)
      railties (>= 4.2.0)
      thor (>= 0.14, < 2.0)
    json (2.12.2)
    jwt (2.10.1)
      base64
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    language_server-protocol (********)
    launchy (3.1.1)
      addressable (~> 2.8)
      childprocess (~> 5.0)
      logger (~> 1.6)
    letter_opener (1.10.0)
      launchy (>= 2.2, < 4)
    lint_roller (1.1.0)
    lockfile (2.1.3)
    logger (1.7.0)
    lograge (0.14.0)
      actionpack (>= 4)
      activesupport (>= 4)
      railties (>= 4)
      request_store (~> 1.0)
    loofah (2.24.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    matrix (0.4.2)
    method_source (1.1.0)
    mini_magick (5.3.0)
      logger
    mini_mime (1.1.5)
    mini_portile2 (2.8.9)
    minitest (5.25.5)
    money (6.19.0)
      i18n (>= 0.6.4, <= 2)
    msgpack (1.7.3)
    multi_json (1.15.0)
    multi_xml (0.7.1)
      bigdecimal (~> 3.1)
    multipart-post (2.4.1)
    mutex_m (0.3.0)
    nanoid (2.0.0)
    net-http (0.6.0)
      uri
    net-imap (0.5.9)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-scp (4.1.0)
      net-ssh (>= 2.6.5, < 8.0.0)
    net-smtp (0.5.1)
      net-protocol
    net-ssh (7.3.0)
    nio4r (2.7.4)
    nokogiri (1.18.9)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    nokogiri (1.18.9-aarch64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.9-aarch64-linux-musl)
      racc (~> 1.4)
    nokogiri (1.18.9-arm-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.9-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.9-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.9-x86_64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.9-x86_64-linux-musl)
      racc (~> 1.4)
    oauth2 (2.0.9)
      faraday (>= 0.17.3, < 3.0)
      jwt (>= 1.0, < 3.0)
      multi_xml (~> 0.5)
      rack (>= 1.2, < 4)
      snaky_hash (~> 2.0)
      version_gem (~> 1.1)
    omniauth (2.1.3)
      hashie (>= 3.4.6)
      rack (>= 2.2.3)
      rack-protection
    omniauth-google-oauth2 (1.2.1)
      jwt (>= 2.9.2)
      oauth2 (~> 2.0)
      omniauth (~> 2.0)
      omniauth-oauth2 (~> 1.8)
    omniauth-oauth2 (1.8.0)
      oauth2 (>= 1.4, < 3)
      omniauth (~> 2.0)
    omniauth-rails_csrf_protection (1.0.2)
      actionpack (>= 4.2)
      omniauth (~> 2.0)
    orm_adapter (0.5.0)
    os (1.1.4)
    ostruct (0.6.3)
    pagy (9.3.5)
    paper_trail (16.0.0)
      activerecord (>= 6.1)
      request_store (~> 1.4)
    parallel (1.27.0)
    parallel_tests (5.3.0)
      parallel
    parser (3.3.8.0)
      ast (~> 2.4.1)
      racc
    pastel (0.8.0)
      tty-color (~> 0.5)
    pg (1.5.9)
    pg_lock (1.0.0)
    phonelib (0.10.10)
    pp (0.6.2)
      prettyprint
    prettyprint (0.2.0)
    prism (1.4.0)
    pry (0.15.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    pry-byebug (3.11.0)
      byebug (~> 12.0)
      pry (>= 0.13, < 0.16)
    psych (5.2.6)
      date
      stringio
    public_suffix (6.0.2)
    public_uid (2.2.0)
      activerecord (> 4.2)
    puma (6.6.0)
      nio4r (~> 2.0)
    raabro (1.4.0)
    racc (1.8.1)
    rack (3.1.16)
    rack-attack (6.7.0)
      rack (>= 1.0, < 4)
    rack-cors (3.0.0)
      logger
      rack (>= 3.0.14)
    rack-protection (4.0.0)
      base64 (>= 0.1.0)
      rack (>= 3.0.0, < 4)
    rack-session (2.1.1)
      base64 (>= 0.1.0)
      rack (>= 3.0.0)
    rack-test (2.2.0)
      rack (>= 1.3)
    rack-timeout (0.7.0)
    rackup (2.2.1)
      rack (>= 3)
    rails (8.0.2)
      actioncable (= 8.0.2)
      actionmailbox (= 8.0.2)
      actionmailer (= 8.0.2)
      actionpack (= 8.0.2)
      actiontext (= 8.0.2)
      actionview (= 8.0.2)
      activejob (= 8.0.2)
      activemodel (= 8.0.2)
      activerecord (= 8.0.2)
      activestorage (= 8.0.2)
      activesupport (= 8.0.2)
      bundler (>= 1.15.0)
      railties (= 8.0.2)
    rails-controller-testing (1.0.5)
      actionpack (>= 5.0.1.rc1)
      actionview (>= 5.0.1.rc1)
      activesupport (>= 5.0.1.rc1)
    rails-dom-testing (2.3.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    rails_charts (0.0.6)
      rails
    railties (8.0.2)
      actionpack (= 8.0.2)
      activesupport (= 8.0.2)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.3.0)
    ransack (4.3.0)
      activerecord (>= 6.1.5)
      activesupport (>= 6.1.5)
      i18n
    rdoc (6.14.2)
      erb
      psych (>= 4.0.0)
    redcarpet (3.6.0)
    redis (5.4.1)
      redis-client (>= 0.22.0)
    redis-client (0.25.1)
      connection_pool
    redis-session-store (0.11.6)
      actionpack (>= 5.2.4.1, < 9)
      redis (>= 3, < 6)
    regexp_parser (2.10.0)
    reline (0.6.2)
      io-console (~> 0.5)
    representable (3.2.0)
      declarative (< 0.1.0)
      trailblazer-option (>= 0.1.1, < 0.2.0)
      uber (< 0.2.0)
    request_store (1.7.0)
      rack (>= 1.4)
    require_all (3.0.0)
    resolv (0.6.2)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    retriable (3.1.2)
    rexml (3.4.1)
    rich_enums (0.2.0)
      activerecord (>= 7.0, < 9.0)
    rollbar (3.6.2)
    rspec-collection_matchers (1.2.1)
      rspec-expectations (>= 2.99.0.beta1)
    rspec-core (3.13.5)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.5)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-github (3.0.0)
      rspec-core (~> 3.0)
    rspec-mocks (3.13.5)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (8.0.1)
      actionpack (>= 7.2)
      activesupport (>= 7.2)
      railties (>= 7.2)
      rspec-core (~> 3.13)
      rspec-expectations (~> 3.13)
      rspec-mocks (~> 3.13)
      rspec-support (~> 3.13)
    rspec-support (3.13.4)
    rubocop (1.78.0)
      json (~> 2.3)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.45.1, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.45.1)
      parser (>= 3.3.7.2)
      prism (~> 1.4)
    rubocop-capybara (2.22.1)
      lint_roller (~> 1.1)
      rubocop (~> 1.72, >= 1.72.1)
    rubocop-factory_bot (2.27.1)
      lint_roller (~> 1.1)
      rubocop (~> 1.72, >= 1.72.1)
    rubocop-performance (1.25.0)
      lint_roller (~> 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.38.0, < 2.0)
    rubocop-rails (2.32.0)
      activesupport (>= 4.2.0)
      lint_roller (~> 1.1)
      rack (>= 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.44.0, < 2.0)
    rubocop-rspec (3.6.0)
      lint_roller (~> 1.1)
      rubocop (~> 1.72, >= 1.72.1)
    rubocop-rspec_rails (2.31.0)
      lint_roller (~> 1.1)
      rubocop (~> 1.72, >= 1.72.1)
      rubocop-rspec (~> 3.5)
    ruby-openai (8.1.0)
      event_stream_parser (>= 0.3.0, < 2.0.0)
      faraday (>= 1)
      faraday-multipart (>= 1)
    ruby-progressbar (1.13.0)
    ruby-vips (2.2.3)
      ffi (~> 1.12)
      logger
    ruby2_keywords (0.0.5)
    rubyzip (2.4.1)
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc-rails (2.1.2)
      railties (>= 4.0.0)
      sassc (>= 2.0)
      sprockets (> 3.0)
      sprockets-rails
      tilt
    scout_apm (5.6.4)
      parser
    securerandom (0.4.1)
    selenium-webdriver (4.34.0)
      base64 (~> 0.2)
      logger (~> 1.4)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2, < 3.0)
      websocket (~> 1.0)
    set_as_primary (0.1.4)
      activesupport (>= 6.1)
    shoulda-matchers (6.5.0)
      activesupport (>= 5.2.0)
    sidekiq (8.0.6)
      connection_pool (>= 2.5.0)
      json (>= 2.9.0)
      logger (>= 1.6.2)
      rack (>= 3.1.0)
      redis-client (>= 0.23.2)
    sidekiq-cron (2.3.0)
      cronex (>= 0.13.0)
      fugit (~> 1.8, >= 1.11.1)
      globalid (>= 1.0.1)
      sidekiq (>= 6.5.0)
    sidekiq-unique-jobs (8.0.11)
      concurrent-ruby (~> 1.0, >= 1.0.5)
      sidekiq (>= 7.0.0, < 9.0.0)
      thor (>= 1.0, < 3.0)
    sigdump (0.2.5)
    signet (0.20.0)
      addressable (~> 2.8)
      faraday (>= 0.17.5, < 3.a)
      jwt (>= 1.5, < 3.0)
      multi_json (~> 1.10)
    sin_lru_redux (2.5.0)
    slack-notifier (2.4.0)
    slim (5.2.1)
      temple (~> 0.10.0)
      tilt (>= 2.1.0)
    snaky_hash (2.0.1)
      hashie
      version_gem (~> 1.1, >= 1.1.1)
    sprockets (4.2.1)
      concurrent-ruby (~> 1.0)
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.5.2)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      sprockets (>= 3.0.0)
    stimulus-rails (1.3.4)
      railties (>= 6.0.0)
    stringio (3.1.7)
    strings (0.2.1)
      strings-ansi (~> 0.2)
      unicode-display_width (>= 1.5, < 3.0)
      unicode_utils (~> 1.4)
    strings-ansi (0.2.0)
    strip_attributes (2.0.0)
      activemodel (>= 3.0, < 9.0)
    stripe (13.3.1)
    strong_migrations (2.4.0)
      activerecord (>= 7.1)
    tailwind_merge (0.16.0)
      sin_lru_redux (~> 2.5)
    tailwindcss-rails (3.3.2)
      railties (>= 7.0.0)
      tailwindcss-ruby (~> 3.0)
    tailwindcss-ruby (3.4.17)
    tailwindcss-ruby (3.4.17-aarch64-linux)
    tailwindcss-ruby (3.4.17-arm-linux)
    tailwindcss-ruby (3.4.17-arm64-darwin)
    tailwindcss-ruby (3.4.17-x86_64-darwin)
    tailwindcss-ruby (3.4.17-x86_64-linux)
    temping (4.3.0)
      activerecord (>= 6.0, < 8.1)
      activesupport (>= 6.0, < 8.1)
    temple (0.10.3)
    thor (1.4.0)
    thread_safe (0.3.6)
    tilt (2.4.0)
    timecop (0.9.10)
    timeout (0.4.3)
    tod (3.1.2)
    trailblazer-option (0.1.2)
    tty-color (0.6.0)
    tty-cursor (0.7.1)
    tty-progressbar (0.18.3)
      strings-ansi (~> 0.2)
      tty-cursor (~> 0.7)
      tty-screen (~> 0.8)
      unicode-display_width (>= 1.6, < 3.0)
    tty-prompt (0.23.1)
      pastel (~> 0.8)
      tty-reader (~> 0.8)
    tty-reader (0.9.0)
      tty-cursor (~> 0.7)
      tty-screen (~> 0.8)
      wisper (~> 2.0)
    tty-screen (0.8.2)
    tty-spinner (0.9.3)
      tty-cursor (~> 0.7)
    tty-table (0.12.0)
      pastel (~> 0.8)
      strings (~> 0.2.0)
      tty-screen (~> 0.8)
    turbo-rails (2.0.13)
      actionpack (>= 7.1.0)
      railties (>= 7.1.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    uber (0.1.0)
    unicode (*******)
    unicode-display_width (2.6.0)
    unicode_utils (1.4.0)
    uniform_notifier (1.16.0)
    uri (1.0.3)
    useragent (0.16.11)
    vcr (6.3.1)
      base64
    version_gem (1.1.4)
    virtus (1.0.5)
      axiom-types (~> 0.1)
      coercible (~> 1.0)
      descendants_tracker (~> 0.0, >= 0.0.3)
      equalizer (~> 0.0, >= 0.0.9)
    warden (1.2.9)
      rack (>= 2.0.9)
    web-console (4.2.1)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    webmock (3.25.1)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    websocket (1.2.11)
    websocket-driver (0.8.0)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    wisper (2.0.1)
    xdan-datetimepicker-rails (2.5.4)
      jquery-rails
      rails (>= 3.2.16)
    xpath (3.2.0)
      nokogiri (~> 1.8)
    zeitwerk (2.7.3)

PLATFORMS
  aarch64-linux
  aarch64-linux-musl
  arm-linux
  arm64-darwin-23
  arm64-darwin-24
  x86-linux
  x86_64-darwin
  x86_64-linux
  x86_64-linux-musl

DEPENDENCIES
  active_storage_validations (~> 2.0)
  activeadmin (~> 3.2)
  activeadmin_addons (~> 1.10)
  activeadmin_json_editor (~> 0.0.10)
  affirm-ruby (~> 1.2.0)
  annotaterb (~> 4.13)
  aws-sdk-cloudfront (~> 1.96)
  aws-sdk-s3 (~> 1.151)
  bcrypt (~> 3.1.20)
  bootsnap (~> 1.18)
  bullet (~> 8.0)
  cancancan (~> 3.6)
  capybara (~> 3.40)
  chartkick (~> 5.1)
  db-snapshot!
  debug (~> 1.9)
  devise (~> 4.9)
  dotenv-rails (~> 3.1)
  down (~> 5.4)
  factory_bot_rails (~> 6.4)
  ffaker (~> 2.23)
  font-awesome-sass (~> 6.5)
  geocoder (~> 1.8)
  google-apis-calendar_v3 (~> 0.47.0)
  google-apis-drive_v3 (~> 0.65.0)
  google-apis-forms_v1 (~> 0.18.0)
  google-cloud-bigquery (~> 1.0)
  hiredis (~> 0.6.3)
  hiredis-client (~> 0.25.0)
  html_to_plain_text (~> 1.0)
  httpx (~> 1.3)
  image_processing (~> 1.2)
  importmap-rails (~> 2.0)
  indefinite_article (~> 0.2.5)
  jbuilder (~> 2.12)
  launchy (~> 3.0)
  letter_opener (~> 1.10)
  lograge (~> 0.14.0)
  mini_magick (~> 5.0)
  money (~> 6.19)
  nanoid (~> 2.0)
  omniauth (~> 2.1)
  omniauth-google-oauth2 (~> 1.1)
  omniauth-rails_csrf_protection (~> 1.0)
  pagy (~> 9.0)
  paper_trail (~> 16.0)
  parallel_tests (~> 5.0)
  parameter-store-rails!
  pg (~> 1.5)
  pg_lock (~> 1.0)
  phonelib (~> 0.10.0)
  pry (~> 0.15.0)
  pry-byebug (~> 3.10)
  public_uid (~> 2.2)
  puma (~> 6.4)
  puma-cloudwatch!
  rack-attack (~> 6.7)
  rack-cors (~> 3.0)
  rack-timeout (~> 0.7.0)
  rails (~> 8.0.0, >= 8.0.1)
  rails-controller-testing (~> 1.0)
  rails-remote-access!
  rails_charts (~> 0.0.6)
  redis (~> 5.2)
  redis-session-store (~> 0.11.5)
  resolv (~> 0.6.0)
  rich_enums (~> 0.2.0)
  rollbar (~> 3.5)
  rspec-collection_matchers (~> 1.2)
  rspec-github (~> 3.0)
  rspec-rails (~> 8.0)
  rubocop-capybara (~> 2.21)
  rubocop-factory_bot (~> 2.26)
  rubocop-performance (~> 1.21)
  rubocop-rails (~> 2.25)
  rubocop-rspec (~> 3.0)
  rubocop-rspec_rails (~> 2.30)
  ruby-openai (~> 8.1.0)
  scout_apm (~> 5.6.0)
  selenium-webdriver (~> 4.23)
  set_as_primary (~> 0.1.4)
  shoulda-matchers (~> 6.0)
  sidekiq (~> 8.0)
  sidekiq-cron (~> 2.0)
  sidekiq-unique-jobs (~> 8.0)
  sigdump (~> 0.2.5)
  slack-notifier (~> 2.4)
  slim (~> 5.2)
  sprockets-rails (~> 3.5)
  stimulus-rails (~> 1.3)
  strip_attributes (~> 2.0)
  stripe (~> 13.3)
  strong_migrations (~> 2.4.0)
  tailwind_merge (~> 0.16.0)
  tailwindcss-rails (~> 3.0)
  temping (~> 4.1)
  timecop (~> 0.9.10)
  tod (~> 3.1)
  turbo-rails (~> 2.0)
  tzinfo-data (~> 1.2024)
  vcr (~> 6.2)
  web-console (~> 4.2)
  webmock (~> 3.23)

RUBY VERSION
   ruby 3.4.4p34

BUNDLED WITH
   2.7.1
